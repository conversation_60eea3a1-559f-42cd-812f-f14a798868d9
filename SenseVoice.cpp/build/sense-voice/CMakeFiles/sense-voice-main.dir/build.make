# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

# Include any dependencies generated for this target.
include sense-voice/CMakeFiles/sense-voice-main.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include sense-voice/CMakeFiles/sense-voice-main.dir/compiler_depend.make

# Include the progress variables for this target.
include sense-voice/CMakeFiles/sense-voice-main.dir/progress.make

# Include the compile flags for this target's objects.
include sense-voice/CMakeFiles/sense-voice-main.dir/flags.make

sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.o: sense-voice/CMakeFiles/sense-voice-main.dir/flags.make
sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/main.cc
sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.o: sense-voice/CMakeFiles/sense-voice-main.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.o -MF CMakeFiles/sense-voice-main.dir/csrc/main.cc.o.d -o CMakeFiles/sense-voice-main.dir/csrc/main.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/main.cc

sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-main.dir/csrc/main.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/main.cc > CMakeFiles/sense-voice-main.dir/csrc/main.cc.i

sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-main.dir/csrc/main.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/main.cc -o CMakeFiles/sense-voice-main.dir/csrc/main.cc.s

# Object files for target sense-voice-main
sense__voice__main_OBJECTS = \
"CMakeFiles/sense-voice-main.dir/csrc/main.cc.o"

# External object files for target sense-voice-main
sense__voice__main_EXTERNAL_OBJECTS =

bin/sense-voice-main: sense-voice/CMakeFiles/sense-voice-main.dir/csrc/main.cc.o
bin/sense-voice-main: sense-voice/CMakeFiles/sense-voice-main.dir/build.make
bin/sense-voice-main: lib/libsense-voice-core.a
bin/sense-voice-main: lib/libggml.dylib
bin/sense-voice-main: lib/libggml-cpu.dylib
bin/sense-voice-main: lib/libggml-blas.dylib
bin/sense-voice-main: lib/libggml-metal.dylib
bin/sense-voice-main: lib/libggml-base.dylib
bin/sense-voice-main: sense-voice/CMakeFiles/sense-voice-main.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ../bin/sense-voice-main"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sense-voice-main.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
sense-voice/CMakeFiles/sense-voice-main.dir/build: bin/sense-voice-main
.PHONY : sense-voice/CMakeFiles/sense-voice-main.dir/build

sense-voice/CMakeFiles/sense-voice-main.dir/clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice && $(CMAKE_COMMAND) -P CMakeFiles/sense-voice-main.dir/cmake_clean.cmake
.PHONY : sense-voice/CMakeFiles/sense-voice-main.dir/clean

sense-voice/CMakeFiles/sense-voice-main.dir/depend:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/english_learn/SenseVoice.cpp /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/CMakeFiles/sense-voice-main.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : sense-voice/CMakeFiles/sense-voice-main.dir/depend

