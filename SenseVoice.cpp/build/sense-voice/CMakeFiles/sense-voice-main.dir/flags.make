# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
CXX_DEFINES = -DGGML_BACKEND_SHARED -DGGML_SHARED -DGGML_USE_BLAS -DGGML_USE_CPU -DGGML_USE_METAL

CXX_INCLUDES = -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/include/ggml -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/../include

CXX_FLAGSarm64 =  -Wall -pthread -O3 -O3 -DNDEBUG -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3

CXX_FLAGS =  -Wall -pthread -O3 -O3 -DNDEBUG -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3

