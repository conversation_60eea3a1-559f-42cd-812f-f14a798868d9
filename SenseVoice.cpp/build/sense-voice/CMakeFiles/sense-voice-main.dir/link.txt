/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -pthread -O3 -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -Wl,-search_paths_first -Wl,-headerpad_max_install_names "CMakeFiles/sense-voice-main.dir/csrc/main.cc.o" -o ../bin/sense-voice-main  -Wl,-rpath,/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib ../lib/libsense-voice-core.a ../lib/libggml.dylib ../lib/libggml-cpu.dylib ../lib/libggml-blas.dylib ../lib/libggml-metal.dylib ../lib/libggml-base.dylib
