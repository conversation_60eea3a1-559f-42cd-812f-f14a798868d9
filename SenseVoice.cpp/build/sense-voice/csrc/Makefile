# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc//CMakeFiles/progress.marks
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/rule:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/CMakeFiles/sense-voice-core.dir/rule
.PHONY : sense-voice/csrc/CMakeFiles/sense-voice-core.dir/rule

# Convenience name for target.
sense-voice-core: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/rule
.PHONY : sense-voice-core

# fast build rule for target.
sense-voice-core/fast:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build
.PHONY : sense-voice-core/fast

common.o: common.cc.o
.PHONY : common.o

# target to build an object file
common.cc.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o
.PHONY : common.cc.o

common.i: common.cc.i
.PHONY : common.i

# target to preprocess a source file
common.cc.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.i
.PHONY : common.cc.i

common.s: common.cc.s
.PHONY : common.s

# target to generate assembly for a file
common.cc.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.s
.PHONY : common.cc.s

fftsg.o: fftsg.cc.o
.PHONY : fftsg.o

# target to build an object file
fftsg.cc.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o
.PHONY : fftsg.cc.o

fftsg.i: fftsg.cc.i
.PHONY : fftsg.i

# target to preprocess a source file
fftsg.cc.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.i
.PHONY : fftsg.cc.i

fftsg.s: fftsg.cc.s
.PHONY : fftsg.s

# target to generate assembly for a file
fftsg.cc.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.s
.PHONY : fftsg.cc.s

sense-voice-decoder.o: sense-voice-decoder.cc.o
.PHONY : sense-voice-decoder.o

# target to build an object file
sense-voice-decoder.cc.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o
.PHONY : sense-voice-decoder.cc.o

sense-voice-decoder.i: sense-voice-decoder.cc.i
.PHONY : sense-voice-decoder.i

# target to preprocess a source file
sense-voice-decoder.cc.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.i
.PHONY : sense-voice-decoder.cc.i

sense-voice-decoder.s: sense-voice-decoder.cc.s
.PHONY : sense-voice-decoder.s

# target to generate assembly for a file
sense-voice-decoder.cc.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.s
.PHONY : sense-voice-decoder.cc.s

sense-voice-encoder.o: sense-voice-encoder.cc.o
.PHONY : sense-voice-encoder.o

# target to build an object file
sense-voice-encoder.cc.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o
.PHONY : sense-voice-encoder.cc.o

sense-voice-encoder.i: sense-voice-encoder.cc.i
.PHONY : sense-voice-encoder.i

# target to preprocess a source file
sense-voice-encoder.cc.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.i
.PHONY : sense-voice-encoder.cc.i

sense-voice-encoder.s: sense-voice-encoder.cc.s
.PHONY : sense-voice-encoder.s

# target to generate assembly for a file
sense-voice-encoder.cc.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.s
.PHONY : sense-voice-encoder.cc.s

sense-voice-frontend.o: sense-voice-frontend.cc.o
.PHONY : sense-voice-frontend.o

# target to build an object file
sense-voice-frontend.cc.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o
.PHONY : sense-voice-frontend.cc.o

sense-voice-frontend.i: sense-voice-frontend.cc.i
.PHONY : sense-voice-frontend.i

# target to preprocess a source file
sense-voice-frontend.cc.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.i
.PHONY : sense-voice-frontend.cc.i

sense-voice-frontend.s: sense-voice-frontend.cc.s
.PHONY : sense-voice-frontend.s

# target to generate assembly for a file
sense-voice-frontend.cc.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.s
.PHONY : sense-voice-frontend.cc.s

sense-voice.o: sense-voice.cc.o
.PHONY : sense-voice.o

# target to build an object file
sense-voice.cc.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o
.PHONY : sense-voice.cc.o

sense-voice.i: sense-voice.cc.i
.PHONY : sense-voice.i

# target to preprocess a source file
sense-voice.cc.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.i
.PHONY : sense-voice.cc.i

sense-voice.s: sense-voice.cc.s
.PHONY : sense-voice.s

# target to generate assembly for a file
sense-voice.cc.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.s
.PHONY : sense-voice.cc.s

silero-vad.o: silero-vad.cc.o
.PHONY : silero-vad.o

# target to build an object file
silero-vad.cc.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o
.PHONY : silero-vad.cc.o

silero-vad.i: silero-vad.cc.i
.PHONY : silero-vad.i

# target to preprocess a source file
silero-vad.cc.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.i
.PHONY : silero-vad.cc.i

silero-vad.s: silero-vad.cc.s
.PHONY : silero-vad.s

# target to generate assembly for a file
silero-vad.cc.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.s
.PHONY : silero-vad.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... sense-voice-core"
	@echo "... common.o"
	@echo "... common.i"
	@echo "... common.s"
	@echo "... fftsg.o"
	@echo "... fftsg.i"
	@echo "... fftsg.s"
	@echo "... sense-voice-decoder.o"
	@echo "... sense-voice-decoder.i"
	@echo "... sense-voice-decoder.s"
	@echo "... sense-voice-encoder.o"
	@echo "... sense-voice-encoder.i"
	@echo "... sense-voice-encoder.s"
	@echo "... sense-voice-frontend.o"
	@echo "... sense-voice-frontend.i"
	@echo "... sense-voice-frontend.s"
	@echo "... sense-voice.o"
	@echo "... sense-voice.i"
	@echo "... sense-voice.s"
	@echo "... silero-vad.o"
	@echo "... silero-vad.i"
	@echo "... silero-vad.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

