# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

# Include any dependencies generated for this target.
include sense-voice/csrc/CMakeFiles/sense-voice-core.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.make

# Include the progress variables for this target.
include sense-voice/csrc/CMakeFiles/sense-voice-core.dir/progress.make

# Include the compile flags for this target's objects.
include sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/common.cc
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o -MF CMakeFiles/sense-voice-core.dir/common.cc.o.d -o CMakeFiles/sense-voice-core.dir/common.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/common.cc

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-core.dir/common.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/common.cc > CMakeFiles/sense-voice-core.dir/common.cc.i

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-core.dir/common.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/common.cc -o CMakeFiles/sense-voice-core.dir/common.cc.s

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-frontend.cc
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o -MF CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o.d -o CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-frontend.cc

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-frontend.cc > CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.i

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-frontend.cc -o CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.s

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/fftsg.cc
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o -MF CMakeFiles/sense-voice-core.dir/fftsg.cc.o.d -o CMakeFiles/sense-voice-core.dir/fftsg.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/fftsg.cc

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-core.dir/fftsg.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/fftsg.cc > CMakeFiles/sense-voice-core.dir/fftsg.cc.i

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-core.dir/fftsg.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/fftsg.cc -o CMakeFiles/sense-voice-core.dir/fftsg.cc.s

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-encoder.cc
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o -MF CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o.d -o CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-encoder.cc

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-encoder.cc > CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.i

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-encoder.cc -o CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.s

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-decoder.cc
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o -MF CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o.d -o CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-decoder.cc

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-decoder.cc > CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.i

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-decoder.cc -o CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.s

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice.cc
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o -MF CMakeFiles/sense-voice-core.dir/sense-voice.cc.o.d -o CMakeFiles/sense-voice-core.dir/sense-voice.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice.cc

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-core.dir/sense-voice.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice.cc > CMakeFiles/sense-voice-core.dir/sense-voice.cc.i

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-core.dir/sense-voice.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice.cc -o CMakeFiles/sense-voice-core.dir/sense-voice.cc.s

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/flags.make
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/silero-vad.cc
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o -MF CMakeFiles/sense-voice-core.dir/silero-vad.cc.o.d -o CMakeFiles/sense-voice-core.dir/silero-vad.cc.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/silero-vad.cc

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sense-voice-core.dir/silero-vad.cc.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/silero-vad.cc > CMakeFiles/sense-voice-core.dir/silero-vad.cc.i

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sense-voice-core.dir/silero-vad.cc.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/silero-vad.cc -o CMakeFiles/sense-voice-core.dir/silero-vad.cc.s

# Object files for target sense-voice-core
sense__voice__core_OBJECTS = \
"CMakeFiles/sense-voice-core.dir/common.cc.o" \
"CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o" \
"CMakeFiles/sense-voice-core.dir/fftsg.cc.o" \
"CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o" \
"CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o" \
"CMakeFiles/sense-voice-core.dir/sense-voice.cc.o" \
"CMakeFiles/sense-voice-core.dir/silero-vad.cc.o"

# External object files for target sense-voice-core
sense__voice__core_EXTERNAL_OBJECTS =

lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make
lib/libsense-voice-core.a: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX static library ../../lib/libsense-voice-core.a"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && $(CMAKE_COMMAND) -P CMakeFiles/sense-voice-core.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sense-voice-core.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build: lib/libsense-voice-core.a
.PHONY : sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc && $(CMAKE_COMMAND) -P CMakeFiles/sense-voice-core.dir/cmake_clean.cmake
.PHONY : sense-voice/csrc/CMakeFiles/sense-voice-core.dir/clean

sense-voice/csrc/CMakeFiles/sense-voice-core.dir/depend:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/english_learn/SenseVoice.cpp /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/CMakeFiles/sense-voice-core.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : sense-voice/csrc/CMakeFiles/sense-voice-core.dir/depend

