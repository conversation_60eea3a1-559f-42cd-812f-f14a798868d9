
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/common.cc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o" "gcc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/common.cc.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/fftsg.cc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o" "gcc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/fftsg.cc.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-decoder.cc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o" "gcc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-decoder.cc.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-encoder.cc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o" "gcc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-encoder.cc.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice-frontend.cc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o" "gcc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice-frontend.cc.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/sense-voice.cc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o" "gcc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/sense-voice.cc.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/silero-vad.cc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o" "gcc" "sense-voice/csrc/CMakeFiles/sense-voice-core.dir/silero-vad.cc.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
