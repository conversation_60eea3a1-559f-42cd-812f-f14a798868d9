/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -dynamiclib -Wl,-headerpad_max_install_names -o ../../../../../../lib/libggml-metal.dylib -install_name @rpath/libggml-metal.dylib "CMakeFiles/ggml-metal.dir/ggml-metal.m.o" "CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o"  -Wl,-rpath,/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib ../../../../../../lib/libggml-base.dylib -framework Foundation -framework Metal -framework MetalKit
