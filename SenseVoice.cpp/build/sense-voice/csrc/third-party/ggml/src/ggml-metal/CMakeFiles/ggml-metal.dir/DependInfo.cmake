
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "ASM"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_ASM
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s" "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o"
  )
set(CMAKE_ASM_COMPILER_ID "AppleClang")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_ASM
  "GGML_BACKEND_BUILD"
  "GGML_BACKEND_SHARED"
  "GGML_METAL_EMBED_LIBRARY"
  "GGML_SCHED_MAX_COPIES=4"
  "GGML_SHARED"
  "_DARWIN_C_SOURCE"
  "_XOPEN_SOURCE=600"
  "ggml_metal_EXPORTS"
  )

# The include file search paths:
set(CMAKE_ASM_TARGET_INCLUDE_PATH
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/include/ggml"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/.."
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/../include"
  "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Foundation.framework"
  "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Metal.framework"
  "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/MetalKit.framework"
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal.m" "sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o" "gcc" "sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
