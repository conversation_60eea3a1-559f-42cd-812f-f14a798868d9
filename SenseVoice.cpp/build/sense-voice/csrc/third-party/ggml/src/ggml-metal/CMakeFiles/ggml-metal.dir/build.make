# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

# Include any dependencies generated for this target.
include sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/compiler_depend.make

# Include the progress variables for this target.
include sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/progress.make

# Include the compile flags for this target's objects.
include sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/flags.make

autogenerated/ggml-metal-embed.s: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-common.h
autogenerated/ggml-metal-embed.s: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal.metal
autogenerated/ggml-metal-embed.s: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal-impl.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generate assembly for embedded Metal library"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && echo Embedding\ Metal\ library
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && sed -e '/__embed_ggml-common.h__/r /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/../ggml-common.h' -e '/__embed_ggml-common.h__/d' < /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal.metal > /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.metal.tmp
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && sed -e '/#include "ggml-metal-impl.h"/r /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal-impl.h' -e '/#include "ggml-metal-impl.h"/d' < /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.metal.tmp > /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.metal
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && echo .section\ __DATA,__ggml_metallib > /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && echo .globl\ _ggml_metallib_start >> /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && echo _ggml_metallib_start: >> /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && echo .incbin\ \"/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.metal\" >> /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && echo .globl\ _ggml_metallib_end >> /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && echo _ggml_metallib_end: >> /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/flags.make
sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal.m
sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o -MF CMakeFiles/ggml-metal.dir/ggml-metal.m.o.d -o CMakeFiles/ggml-metal.dir/ggml-metal.m.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal.m

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/ggml-metal.dir/ggml-metal.m.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal.m > CMakeFiles/ggml-metal.dir/ggml-metal.m.i

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/ggml-metal.dir/ggml-metal.m.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal/ggml-metal.m -o CMakeFiles/ggml-metal.dir/ggml-metal.m.s

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/flags.make
sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o: autogenerated/ggml-metal-embed.s
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building ASM object sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -o CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing ASM source to CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s > CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.i

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling ASM source to assembly CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/autogenerated/ggml-metal-embed.s -o CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.s

# Object files for target ggml-metal
ggml__metal_OBJECTS = \
"CMakeFiles/ggml-metal.dir/ggml-metal.m.o" \
"CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o"

# External object files for target ggml-metal
ggml__metal_EXTERNAL_OBJECTS =

lib/libggml-metal.dylib: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/ggml-metal.m.o
lib/libggml-metal.dylib: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/__/__/__/__/__/__/autogenerated/ggml-metal-embed.s.o
lib/libggml-metal.dylib: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/build.make
lib/libggml-metal.dylib: lib/libggml-base.dylib
lib/libggml-metal.dylib: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking C shared library ../../../../../../lib/libggml-metal.dylib"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ggml-metal.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/build: lib/libggml-metal.dylib
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/build

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal && $(CMAKE_COMMAND) -P CMakeFiles/ggml-metal.dir/cmake_clean.cmake
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/clean

sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/depend: autogenerated/ggml-metal-embed.s
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/english_learn/SenseVoice.cpp /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-metal /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/depend

