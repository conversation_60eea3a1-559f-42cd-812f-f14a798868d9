# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src//CMakeFiles/progress.marks
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/rule:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/rule
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/rule

# Convenience name for target.
ggml-base: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/rule
.PHONY : ggml-base

# fast build rule for target.
ggml-base/fast:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build
.PHONY : ggml-base/fast

# Convenience name for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/rule:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/rule
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/rule

# Convenience name for target.
ggml: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/rule
.PHONY : ggml

# fast build rule for target.
ggml/fast:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build
.PHONY : ggml/fast

# Convenience name for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/rule:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/rule
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/rule

# Convenience name for target.
ggml-cpu: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/rule
.PHONY : ggml-cpu

# fast build rule for target.
ggml-cpu/fast:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build
.PHONY : ggml-cpu/fast

ggml-alloc.o: ggml-alloc.c.o
.PHONY : ggml-alloc.o

# target to build an object file
ggml-alloc.c.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o
.PHONY : ggml-alloc.c.o

ggml-alloc.i: ggml-alloc.c.i
.PHONY : ggml-alloc.i

# target to preprocess a source file
ggml-alloc.c.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.i
.PHONY : ggml-alloc.c.i

ggml-alloc.s: ggml-alloc.c.s
.PHONY : ggml-alloc.s

# target to generate assembly for a file
ggml-alloc.c.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.s
.PHONY : ggml-alloc.c.s

ggml-backend-reg.o: ggml-backend-reg.cpp.o
.PHONY : ggml-backend-reg.o

# target to build an object file
ggml-backend-reg.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/ggml-backend-reg.cpp.o
.PHONY : ggml-backend-reg.cpp.o

ggml-backend-reg.i: ggml-backend-reg.cpp.i
.PHONY : ggml-backend-reg.i

# target to preprocess a source file
ggml-backend-reg.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/ggml-backend-reg.cpp.i
.PHONY : ggml-backend-reg.cpp.i

ggml-backend-reg.s: ggml-backend-reg.cpp.s
.PHONY : ggml-backend-reg.s

# target to generate assembly for a file
ggml-backend-reg.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/ggml-backend-reg.cpp.s
.PHONY : ggml-backend-reg.cpp.s

ggml-backend.o: ggml-backend.cpp.o
.PHONY : ggml-backend.o

# target to build an object file
ggml-backend.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o
.PHONY : ggml-backend.cpp.o

ggml-backend.i: ggml-backend.cpp.i
.PHONY : ggml-backend.i

# target to preprocess a source file
ggml-backend.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.i
.PHONY : ggml-backend.cpp.i

ggml-backend.s: ggml-backend.cpp.s
.PHONY : ggml-backend.s

# target to generate assembly for a file
ggml-backend.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.s
.PHONY : ggml-backend.cpp.s

ggml-cpu/amx/amx.o: ggml-cpu/amx/amx.cpp.o
.PHONY : ggml-cpu/amx/amx.o

# target to build an object file
ggml-cpu/amx/amx.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o
.PHONY : ggml-cpu/amx/amx.cpp.o

ggml-cpu/amx/amx.i: ggml-cpu/amx/amx.cpp.i
.PHONY : ggml-cpu/amx/amx.i

# target to preprocess a source file
ggml-cpu/amx/amx.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.i
.PHONY : ggml-cpu/amx/amx.cpp.i

ggml-cpu/amx/amx.s: ggml-cpu/amx/amx.cpp.s
.PHONY : ggml-cpu/amx/amx.s

# target to generate assembly for a file
ggml-cpu/amx/amx.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.s
.PHONY : ggml-cpu/amx/amx.cpp.s

ggml-cpu/amx/mmq.o: ggml-cpu/amx/mmq.cpp.o
.PHONY : ggml-cpu/amx/mmq.o

# target to build an object file
ggml-cpu/amx/mmq.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o
.PHONY : ggml-cpu/amx/mmq.cpp.o

ggml-cpu/amx/mmq.i: ggml-cpu/amx/mmq.cpp.i
.PHONY : ggml-cpu/amx/mmq.i

# target to preprocess a source file
ggml-cpu/amx/mmq.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.i
.PHONY : ggml-cpu/amx/mmq.cpp.i

ggml-cpu/amx/mmq.s: ggml-cpu/amx/mmq.cpp.s
.PHONY : ggml-cpu/amx/mmq.s

# target to generate assembly for a file
ggml-cpu/amx/mmq.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.s
.PHONY : ggml-cpu/amx/mmq.cpp.s

ggml-cpu/ggml-cpu-aarch64.o: ggml-cpu/ggml-cpu-aarch64.cpp.o
.PHONY : ggml-cpu/ggml-cpu-aarch64.o

# target to build an object file
ggml-cpu/ggml-cpu-aarch64.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o
.PHONY : ggml-cpu/ggml-cpu-aarch64.cpp.o

ggml-cpu/ggml-cpu-aarch64.i: ggml-cpu/ggml-cpu-aarch64.cpp.i
.PHONY : ggml-cpu/ggml-cpu-aarch64.i

# target to preprocess a source file
ggml-cpu/ggml-cpu-aarch64.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.i
.PHONY : ggml-cpu/ggml-cpu-aarch64.cpp.i

ggml-cpu/ggml-cpu-aarch64.s: ggml-cpu/ggml-cpu-aarch64.cpp.s
.PHONY : ggml-cpu/ggml-cpu-aarch64.s

# target to generate assembly for a file
ggml-cpu/ggml-cpu-aarch64.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.s
.PHONY : ggml-cpu/ggml-cpu-aarch64.cpp.s

ggml-cpu/ggml-cpu-hbm.o: ggml-cpu/ggml-cpu-hbm.cpp.o
.PHONY : ggml-cpu/ggml-cpu-hbm.o

# target to build an object file
ggml-cpu/ggml-cpu-hbm.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o
.PHONY : ggml-cpu/ggml-cpu-hbm.cpp.o

ggml-cpu/ggml-cpu-hbm.i: ggml-cpu/ggml-cpu-hbm.cpp.i
.PHONY : ggml-cpu/ggml-cpu-hbm.i

# target to preprocess a source file
ggml-cpu/ggml-cpu-hbm.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.i
.PHONY : ggml-cpu/ggml-cpu-hbm.cpp.i

ggml-cpu/ggml-cpu-hbm.s: ggml-cpu/ggml-cpu-hbm.cpp.s
.PHONY : ggml-cpu/ggml-cpu-hbm.s

# target to generate assembly for a file
ggml-cpu/ggml-cpu-hbm.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.s
.PHONY : ggml-cpu/ggml-cpu-hbm.cpp.s

ggml-cpu/ggml-cpu-quants.o: ggml-cpu/ggml-cpu-quants.c.o
.PHONY : ggml-cpu/ggml-cpu-quants.o

# target to build an object file
ggml-cpu/ggml-cpu-quants.c.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o
.PHONY : ggml-cpu/ggml-cpu-quants.c.o

ggml-cpu/ggml-cpu-quants.i: ggml-cpu/ggml-cpu-quants.c.i
.PHONY : ggml-cpu/ggml-cpu-quants.i

# target to preprocess a source file
ggml-cpu/ggml-cpu-quants.c.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.i
.PHONY : ggml-cpu/ggml-cpu-quants.c.i

ggml-cpu/ggml-cpu-quants.s: ggml-cpu/ggml-cpu-quants.c.s
.PHONY : ggml-cpu/ggml-cpu-quants.s

# target to generate assembly for a file
ggml-cpu/ggml-cpu-quants.c.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.s
.PHONY : ggml-cpu/ggml-cpu-quants.c.s

ggml-cpu/ggml-cpu-traits.o: ggml-cpu/ggml-cpu-traits.cpp.o
.PHONY : ggml-cpu/ggml-cpu-traits.o

# target to build an object file
ggml-cpu/ggml-cpu-traits.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o
.PHONY : ggml-cpu/ggml-cpu-traits.cpp.o

ggml-cpu/ggml-cpu-traits.i: ggml-cpu/ggml-cpu-traits.cpp.i
.PHONY : ggml-cpu/ggml-cpu-traits.i

# target to preprocess a source file
ggml-cpu/ggml-cpu-traits.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.i
.PHONY : ggml-cpu/ggml-cpu-traits.cpp.i

ggml-cpu/ggml-cpu-traits.s: ggml-cpu/ggml-cpu-traits.cpp.s
.PHONY : ggml-cpu/ggml-cpu-traits.s

# target to generate assembly for a file
ggml-cpu/ggml-cpu-traits.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.s
.PHONY : ggml-cpu/ggml-cpu-traits.cpp.s

ggml-cpu/ggml-cpu.o: ggml-cpu/ggml-cpu.c.o
.PHONY : ggml-cpu/ggml-cpu.o

# target to build an object file
ggml-cpu/ggml-cpu.c.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o
.PHONY : ggml-cpu/ggml-cpu.c.o

ggml-cpu/ggml-cpu.i: ggml-cpu/ggml-cpu.c.i
.PHONY : ggml-cpu/ggml-cpu.i

# target to preprocess a source file
ggml-cpu/ggml-cpu.c.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.i
.PHONY : ggml-cpu/ggml-cpu.c.i

ggml-cpu/ggml-cpu.s: ggml-cpu/ggml-cpu.c.s
.PHONY : ggml-cpu/ggml-cpu.s

# target to generate assembly for a file
ggml-cpu/ggml-cpu.c.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.s
.PHONY : ggml-cpu/ggml-cpu.c.s

ggml-cpu/ggml-cpu.o: ggml-cpu/ggml-cpu.cpp.o
.PHONY : ggml-cpu/ggml-cpu.o

# target to build an object file
ggml-cpu/ggml-cpu.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o
.PHONY : ggml-cpu/ggml-cpu.cpp.o

ggml-cpu/ggml-cpu.i: ggml-cpu/ggml-cpu.cpp.i
.PHONY : ggml-cpu/ggml-cpu.i

# target to preprocess a source file
ggml-cpu/ggml-cpu.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.i
.PHONY : ggml-cpu/ggml-cpu.cpp.i

ggml-cpu/ggml-cpu.s: ggml-cpu/ggml-cpu.cpp.s
.PHONY : ggml-cpu/ggml-cpu.s

# target to generate assembly for a file
ggml-cpu/ggml-cpu.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.s
.PHONY : ggml-cpu/ggml-cpu.cpp.s

ggml-opt.o: ggml-opt.cpp.o
.PHONY : ggml-opt.o

# target to build an object file
ggml-opt.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o
.PHONY : ggml-opt.cpp.o

ggml-opt.i: ggml-opt.cpp.i
.PHONY : ggml-opt.i

# target to preprocess a source file
ggml-opt.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.i
.PHONY : ggml-opt.cpp.i

ggml-opt.s: ggml-opt.cpp.s
.PHONY : ggml-opt.s

# target to generate assembly for a file
ggml-opt.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.s
.PHONY : ggml-opt.cpp.s

ggml-quants.o: ggml-quants.c.o
.PHONY : ggml-quants.o

# target to build an object file
ggml-quants.c.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o
.PHONY : ggml-quants.c.o

ggml-quants.i: ggml-quants.c.i
.PHONY : ggml-quants.i

# target to preprocess a source file
ggml-quants.c.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.i
.PHONY : ggml-quants.c.i

ggml-quants.s: ggml-quants.c.s
.PHONY : ggml-quants.s

# target to generate assembly for a file
ggml-quants.c.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.s
.PHONY : ggml-quants.c.s

ggml-threading.o: ggml-threading.cpp.o
.PHONY : ggml-threading.o

# target to build an object file
ggml-threading.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o
.PHONY : ggml-threading.cpp.o

ggml-threading.i: ggml-threading.cpp.i
.PHONY : ggml-threading.i

# target to preprocess a source file
ggml-threading.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.i
.PHONY : ggml-threading.cpp.i

ggml-threading.s: ggml-threading.cpp.s
.PHONY : ggml-threading.s

# target to generate assembly for a file
ggml-threading.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.s
.PHONY : ggml-threading.cpp.s

ggml.o: ggml.c.o
.PHONY : ggml.o

# target to build an object file
ggml.c.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o
.PHONY : ggml.c.o

ggml.i: ggml.c.i
.PHONY : ggml.i

# target to preprocess a source file
ggml.c.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.i
.PHONY : ggml.c.i

ggml.s: ggml.c.s
.PHONY : ggml.s

# target to generate assembly for a file
ggml.c.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.s
.PHONY : ggml.c.s

gguf.o: gguf.cpp.o
.PHONY : gguf.o

# target to build an object file
gguf.cpp.o:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o
.PHONY : gguf.cpp.o

gguf.i: gguf.cpp.i
.PHONY : gguf.i

# target to preprocess a source file
gguf.cpp.i:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.i
.PHONY : gguf.cpp.i

gguf.s: gguf.cpp.s
.PHONY : gguf.s

# target to generate assembly for a file
gguf.cpp.s:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.s
.PHONY : gguf.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... ggml"
	@echo "... ggml-base"
	@echo "... ggml-cpu"
	@echo "... ggml-alloc.o"
	@echo "... ggml-alloc.i"
	@echo "... ggml-alloc.s"
	@echo "... ggml-backend-reg.o"
	@echo "... ggml-backend-reg.i"
	@echo "... ggml-backend-reg.s"
	@echo "... ggml-backend.o"
	@echo "... ggml-backend.i"
	@echo "... ggml-backend.s"
	@echo "... ggml-cpu/amx/amx.o"
	@echo "... ggml-cpu/amx/amx.i"
	@echo "... ggml-cpu/amx/amx.s"
	@echo "... ggml-cpu/amx/mmq.o"
	@echo "... ggml-cpu/amx/mmq.i"
	@echo "... ggml-cpu/amx/mmq.s"
	@echo "... ggml-cpu/ggml-cpu-aarch64.o"
	@echo "... ggml-cpu/ggml-cpu-aarch64.i"
	@echo "... ggml-cpu/ggml-cpu-aarch64.s"
	@echo "... ggml-cpu/ggml-cpu-hbm.o"
	@echo "... ggml-cpu/ggml-cpu-hbm.i"
	@echo "... ggml-cpu/ggml-cpu-hbm.s"
	@echo "... ggml-cpu/ggml-cpu-quants.o"
	@echo "... ggml-cpu/ggml-cpu-quants.i"
	@echo "... ggml-cpu/ggml-cpu-quants.s"
	@echo "... ggml-cpu/ggml-cpu-traits.o"
	@echo "... ggml-cpu/ggml-cpu-traits.i"
	@echo "... ggml-cpu/ggml-cpu-traits.s"
	@echo "... ggml-cpu/ggml-cpu.o"
	@echo "... ggml-cpu/ggml-cpu.i"
	@echo "... ggml-cpu/ggml-cpu.s"
	@echo "... ggml-cpu/ggml-cpu.o"
	@echo "... ggml-cpu/ggml-cpu.i"
	@echo "... ggml-cpu/ggml-cpu.s"
	@echo "... ggml-opt.o"
	@echo "... ggml-opt.i"
	@echo "... ggml-opt.s"
	@echo "... ggml-quants.o"
	@echo "... ggml-quants.i"
	@echo "... ggml-quants.s"
	@echo "... ggml-threading.o"
	@echo "... ggml-threading.i"
	@echo "... ggml-threading.s"
	@echo "... ggml.o"
	@echo "... ggml.i"
	@echo "... ggml.s"
	@echo "... gguf.o"
	@echo "... gguf.i"
	@echo "... gguf.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

