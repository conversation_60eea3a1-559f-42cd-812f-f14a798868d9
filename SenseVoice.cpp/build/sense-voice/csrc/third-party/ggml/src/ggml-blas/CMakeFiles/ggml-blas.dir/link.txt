/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -pthread -O3 -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -dynamiclib -Wl,-headerpad_max_install_names -o ../../../../../../lib/libggml-blas.dylib -install_name @rpath/libggml-blas.dylib "CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o"  -Wl,-rpath,/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib ../../../../../../lib/libggml-base.dylib -framework Accelerate
