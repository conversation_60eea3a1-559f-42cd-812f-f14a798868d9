# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

# Include any dependencies generated for this target.
include sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/compiler_depend.make

# Include the progress variables for this target.
include sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/progress.make

# Include the compile flags for this target's objects.
include sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/flags.make

sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/flags.make
sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-blas/ggml-blas.cpp
sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-blas && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o -MF CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o.d -o CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-blas/ggml-blas.cpp

sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-blas.dir/ggml-blas.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-blas && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-blas/ggml-blas.cpp > CMakeFiles/ggml-blas.dir/ggml-blas.cpp.i

sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-blas.dir/ggml-blas.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-blas && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-blas/ggml-blas.cpp -o CMakeFiles/ggml-blas.dir/ggml-blas.cpp.s

# Object files for target ggml-blas
ggml__blas_OBJECTS = \
"CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o"

# External object files for target ggml-blas
ggml__blas_EXTERNAL_OBJECTS =

lib/libggml-blas.dylib: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/ggml-blas.cpp.o
lib/libggml-blas.dylib: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/build.make
lib/libggml-blas.dylib: lib/libggml-base.dylib
lib/libggml-blas.dylib: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library ../../../../../../lib/libggml-blas.dylib"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-blas && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ggml-blas.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/build: lib/libggml-blas.dylib
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/build

sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-blas && $(CMAKE_COMMAND) -P CMakeFiles/ggml-blas.dir/cmake_clean.cmake
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/clean

sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/depend:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/english_learn/SenseVoice.cpp /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-blas /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-blas /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/depend

