# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
C_DEFINES = -DGGML_BUILD -DGGML_SCHED_MAX_COPIES=4 -DGGML_SHARED -D_DARWIN_C_SOURCE -D_XOPEN_SOURCE=600 -Dggml_base_EXPORTS

C_INCLUDES = -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/include/ggml -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/. -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/../include

C_FLAGSarm64 = -O3 -DNDEBUG -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -fPIC -Wshadow -Wstrict-prototypes -Wpointer-arith -Wmissing-prototypes -Werror=implicit-int -Werror=implicit-function-declaration -Wall -Wextra -Wpedantic -Wcast-qual -Wno-unused-function -Wunreachable-code-break -Wunreachable-code-return -Wdouble-promotion

C_FLAGS = -O3 -DNDEBUG -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -fPIC -Wshadow -Wstrict-prototypes -Wpointer-arith -Wmissing-prototypes -Werror=implicit-int -Werror=implicit-function-declaration -Wall -Wextra -Wpedantic -Wcast-qual -Wno-unused-function -Wunreachable-code-break -Wunreachable-code-return -Wdouble-promotion

CXX_DEFINES = -DGGML_BUILD -DGGML_SCHED_MAX_COPIES=4 -DGGML_SHARED -D_DARWIN_C_SOURCE -D_XOPEN_SOURCE=600 -Dggml_base_EXPORTS

CXX_INCLUDES = -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/include/ggml -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/. -I/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/../include

CXX_FLAGSarm64 =  -Wall -pthread -O3 -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -fPIC -Wmissing-declarations -Wmissing-noreturn -Wall -Wextra -Wpedantic -Wcast-qual -Wno-unused-function -Wunreachable-code-break -Wunreachable-code-return -Wmissing-prototypes -Wextra-semi

CXX_FLAGS =  -Wall -pthread -O3 -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -fPIC -Wmissing-declarations -Wmissing-noreturn -Wall -Wextra -Wpedantic -Wcast-qual -Wno-unused-function -Wunreachable-code-break -Wunreachable-code-return -Wmissing-prototypes -Wextra-semi

