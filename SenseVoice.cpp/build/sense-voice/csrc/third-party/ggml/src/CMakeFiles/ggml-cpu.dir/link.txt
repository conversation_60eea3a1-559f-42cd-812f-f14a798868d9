/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -pthread -O3 -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -dynamiclib -Wl,-headerpad_max_install_names -o ../../../../../lib/libggml-cpu.dylib -install_name @rpath/libggml-cpu.dylib "CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o" "CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o" "CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o" "CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o" "CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o" "CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o" "CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o" "CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o"  -Wl,-rpath,/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib ../../../../../lib/libggml-base.dylib -framework Accelerate
