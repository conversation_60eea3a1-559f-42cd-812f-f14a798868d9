/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -pthread -O3 -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -dynamiclib -Wl,-headerpad_max_install_names -o ../../../../../lib/libggml.dylib -install_name @rpath/libggml.dylib "CMakeFiles/ggml.dir/ggml-backend-reg.cpp.o"  -Wl,-rpath,/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib ../../../../../lib/libggml-cpu.dylib ../../../../../lib/libggml-blas.dylib ../../../../../lib/libggml-metal.dylib ../../../../../lib/libggml-base.dylib
