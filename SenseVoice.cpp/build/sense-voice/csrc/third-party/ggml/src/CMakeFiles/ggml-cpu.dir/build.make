# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

# Include any dependencies generated for this target.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.make

# Include the progress variables for this target.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/progress.make

# Include the compile flags for this target's objects.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.c
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.c

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.c > CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.c -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.cpp > CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.cpp -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-aarch64.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-aarch64.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-aarch64.cpp > CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-aarch64.cpp -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-hbm.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-hbm.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-hbm.cpp > CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-hbm.cpp -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-quants.c
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-quants.c

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-quants.c > CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-quants.c -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-traits.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-traits.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-traits.cpp > CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-traits.cpp -o CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/amx.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/amx.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/amx.cpp > CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/amx.cpp -o CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/mmq.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o -MF CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o.d -o CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/mmq.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/mmq.cpp > CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/mmq.cpp -o CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.s

# Object files for target ggml-cpu
ggml__cpu_OBJECTS = \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o" \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o" \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o" \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o" \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o" \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o" \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o" \
"CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o"

# External object files for target ggml-cpu
ggml__cpu_EXTERNAL_OBJECTS =

lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make
lib/libggml-cpu.dylib: lib/libggml-base.dylib
lib/libggml-cpu.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking CXX shared library ../../../../../lib/libggml-cpu.dylib"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ggml-cpu.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build: lib/libggml-cpu.dylib
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && $(CMAKE_COMMAND) -P CMakeFiles/ggml-cpu.dir/cmake_clean.cmake
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/clean

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/depend:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/english_learn/SenseVoice.cpp /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/depend

