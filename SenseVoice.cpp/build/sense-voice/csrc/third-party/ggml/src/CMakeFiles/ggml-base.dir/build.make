# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

# Include any dependencies generated for this target.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.make

# Include the progress variables for this target.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/progress.make

# Include the compile flags for this target's objects.
include sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml.c
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o -MF CMakeFiles/ggml-base.dir/ggml.c.o.d -o CMakeFiles/ggml-base.dir/ggml.c.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml.c

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/ggml-base.dir/ggml.c.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml.c > CMakeFiles/ggml-base.dir/ggml.c.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/ggml-base.dir/ggml.c.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml.c -o CMakeFiles/ggml-base.dir/ggml.c.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-alloc.c
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o -MF CMakeFiles/ggml-base.dir/ggml-alloc.c.o.d -o CMakeFiles/ggml-base.dir/ggml-alloc.c.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-alloc.c

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/ggml-base.dir/ggml-alloc.c.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-alloc.c > CMakeFiles/ggml-base.dir/ggml-alloc.c.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/ggml-base.dir/ggml-alloc.c.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-alloc.c -o CMakeFiles/ggml-base.dir/ggml-alloc.c.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-backend.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o -MF CMakeFiles/ggml-base.dir/ggml-backend.cpp.o.d -o CMakeFiles/ggml-base.dir/ggml-backend.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-backend.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-base.dir/ggml-backend.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-backend.cpp > CMakeFiles/ggml-base.dir/ggml-backend.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-base.dir/ggml-backend.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-backend.cpp -o CMakeFiles/ggml-base.dir/ggml-backend.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-opt.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o -MF CMakeFiles/ggml-base.dir/ggml-opt.cpp.o.d -o CMakeFiles/ggml-base.dir/ggml-opt.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-opt.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-base.dir/ggml-opt.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-opt.cpp > CMakeFiles/ggml-base.dir/ggml-opt.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-base.dir/ggml-opt.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-opt.cpp -o CMakeFiles/ggml-base.dir/ggml-opt.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-threading.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o -MF CMakeFiles/ggml-base.dir/ggml-threading.cpp.o.d -o CMakeFiles/ggml-base.dir/ggml-threading.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-threading.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-base.dir/ggml-threading.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-threading.cpp > CMakeFiles/ggml-base.dir/ggml-threading.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-base.dir/ggml-threading.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-threading.cpp -o CMakeFiles/ggml-base.dir/ggml-threading.cpp.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-quants.c
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o -MF CMakeFiles/ggml-base.dir/ggml-quants.c.o.d -o CMakeFiles/ggml-base.dir/ggml-quants.c.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-quants.c

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/ggml-base.dir/ggml-quants.c.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-quants.c > CMakeFiles/ggml-base.dir/ggml-quants.c.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/ggml-base.dir/ggml-quants.c.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-quants.c -o CMakeFiles/ggml-base.dir/ggml-quants.c.s

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/flags.make
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/gguf.cpp
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o -MF CMakeFiles/ggml-base.dir/gguf.cpp.o.d -o CMakeFiles/ggml-base.dir/gguf.cpp.o -c /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/gguf.cpp

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ggml-base.dir/gguf.cpp.i"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/gguf.cpp > CMakeFiles/ggml-base.dir/gguf.cpp.i

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ggml-base.dir/gguf.cpp.s"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/gguf.cpp -o CMakeFiles/ggml-base.dir/gguf.cpp.s

# Object files for target ggml-base
ggml__base_OBJECTS = \
"CMakeFiles/ggml-base.dir/ggml.c.o" \
"CMakeFiles/ggml-base.dir/ggml-alloc.c.o" \
"CMakeFiles/ggml-base.dir/ggml-backend.cpp.o" \
"CMakeFiles/ggml-base.dir/ggml-opt.cpp.o" \
"CMakeFiles/ggml-base.dir/ggml-threading.cpp.o" \
"CMakeFiles/ggml-base.dir/ggml-quants.c.o" \
"CMakeFiles/ggml-base.dir/gguf.cpp.o"

# External object files for target ggml-base
ggml__base_EXTERNAL_OBJECTS =

lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make
lib/libggml-base.dylib: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX shared library ../../../../../lib/libggml-base.dylib"
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ggml-base.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build: lib/libggml-base.dylib
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/clean:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src && $(CMAKE_COMMAND) -P CMakeFiles/ggml-base.dir/cmake_clean.cmake
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/clean

sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/depend:
	cd /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/english_learn/SenseVoice.cpp /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/depend

