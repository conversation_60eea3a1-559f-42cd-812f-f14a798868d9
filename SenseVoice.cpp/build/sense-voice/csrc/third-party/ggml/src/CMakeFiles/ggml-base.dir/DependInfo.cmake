
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-alloc.c" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-alloc.c.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-quants.c" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-quants.c.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml.c" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml.c.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-backend.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-backend.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-opt.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-opt.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-threading.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/ggml-threading.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/gguf.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/gguf.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
