
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-quants.c" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-quants.c.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.c" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.c.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/amx.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/amx.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/amx/mmq.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/amx/mmq.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-aarch64.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-aarch64.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-hbm.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-hbm.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu-traits.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu-traits.cpp.o.d"
  "/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml/src/ggml-cpu/ggml-cpu.cpp" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o" "gcc" "sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/ggml-cpu/ggml-cpu.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
