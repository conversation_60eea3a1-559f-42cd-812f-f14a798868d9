# This is the CMakeCache file.
# For build in directory: /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build
# It was generated by CMake: /Applications/CMake.app/Contents/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a library.
ACCELERATE_FRAMEWORK:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Accelerate.framework

//Path to a library.
BLAS_Accelerate_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Accelerate.framework

//ggml: build shared libraries
BUILD_SHARED_LIBS:BOOL=ON

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar

CMAKE_ARCHIVE_OUTPUT_DIRECTORY:STRING=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib

//ASM compiler
CMAKE_ASM_COMPILER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc

//Flags used by the ASM compiler during all build types.
CMAKE_ASM_FLAGS:STRING=

//Flags used by the ASM compiler during DEBUG builds.
CMAKE_ASM_FLAGS_DEBUG:STRING=-g

//Flags used by the ASM compiler during MINSIZEREL builds.
CMAKE_ASM_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the ASM compiler during RELEASE builds.
CMAKE_ASM_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the ASM compiler during RELWITHDEBINFO builds.
CMAKE_ASM_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

CMAKE_LIBRARY_OUTPUT_DIRECTORY:STRING=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib

//Path to a program.
CMAKE_LINKER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=15.3

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=SenseVoice.cpp

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.4.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=4

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

CMAKE_RUNTIME_OUTPUT_DIRECTORY:STRING=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/bin

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a library.
FOUNDATION_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Foundation.framework

//ggml: enable Accelerate framework
GGML_ACCELERATE:BOOL=ON

//ggml: enable all compiler warnings in 3rd party libs
GGML_ALL_WARNINGS_3RD_PARTY:BOOL=OFF

//ggml: enable AMX-BF16
GGML_AMX_BF16:BOOL=OFF

//ggml: enable AMX-INT8
GGML_AMX_INT8:BOOL=OFF

//ggml: enable AMX-TILE
GGML_AMX_TILE:BOOL=OFF

//ggml: enable AVX
GGML_AVX:BOOL=OFF

//ggml: enable AVX2
GGML_AVX2:BOOL=OFF

//ggml: enable AVX512F
GGML_AVX512:BOOL=OFF

//ggml: enable AVX512-BF16
GGML_AVX512_BF16:BOOL=OFF

//ggml: enable AVX512-VBMI
GGML_AVX512_VBMI:BOOL=OFF

//ggml: enable AVX512-VNNI
GGML_AVX512_VNNI:BOOL=OFF

//ggml: enable AVX-VNNI
GGML_AVX_VNNI:BOOL=OFF

//ggml: build backends as dynamic libraries (requires BUILD_SHARED_LIBS)
GGML_BACKEND_DL:BOOL=OFF

//Location of binary  files
GGML_BIN_INSTALL_DIR:PATH=bin

//ggml: use BLAS
GGML_BLAS:BOOL=ON

//ggml: BLAS library vendor
GGML_BLAS_VENDOR:STRING=Apple

//ggml: build examples
GGML_BUILD_EXAMPLES:BOOL=OFF

//ggml: build tests
GGML_BUILD_TESTS:BOOL=OFF

//Path to a program.
GGML_CCACHE_FOUND:FILEPATH=GGML_CCACHE_FOUND-NOTFOUND

//ggml: enable CPU backend
GGML_CPU:BOOL=ON

//ggml: use runtime weight conversion of Q4_0 to Q4_X_X
GGML_CPU_AARCH64:BOOL=ON

//ggml: build all variants of the CPU backend (requires GGML_BACKEND_DL)
GGML_CPU_ALL_VARIANTS:BOOL=OFF

//ggml: CPU architecture for ARM
GGML_CPU_ARM_ARCH:STRING=

//ggml: use memkind for CPU HBM
GGML_CPU_HBM:BOOL=OFF

//ggml: use CUDA
GGML_CUDA:BOOL=OFF

//ggml: use 16 bit floats for some calculations
GGML_CUDA_F16:BOOL=OFF

//ggml: compile all quants for FlashAttention
GGML_CUDA_FA_ALL_QUANTS:BOOL=OFF

//ggml: always use cuBLAS instead of mmq kernels
GGML_CUDA_FORCE_CUBLAS:BOOL=OFF

//ggml: use mmq kernels instead of cuBLAS
GGML_CUDA_FORCE_MMQ:BOOL=OFF

//ggml: use CUDA graphs (llama.cpp only)
GGML_CUDA_GRAPHS:BOOL=OFF

//ggml: do not use peer to peer copies
GGML_CUDA_NO_PEER_COPY:BOOL=OFF

//ggml: do not try to use CUDA VMM
GGML_CUDA_NO_VMM:BOOL=OFF

//ggml: max. batch size for using peer access
GGML_CUDA_PEER_MAX_BATCH_SIZE:STRING=128

//ggml: enable F16C
GGML_F16C:BOOL=OFF

//ggml: enable FMA
GGML_FMA:BOOL=OFF

//ggml: enable gprof
GGML_GPROF:BOOL=OFF

//ggml: use HIP
GGML_HIP:BOOL=OFF

//ggml: use HIP graph, experimental, slow
GGML_HIP_GRAPHS:BOOL=OFF

//ggml: do not try to use HIP VMM
GGML_HIP_NO_VMM:BOOL=ON

//ggml: use HIP unified memory architecture
GGML_HIP_UMA:BOOL=OFF

//Location of header  files
GGML_INCLUDE_INSTALL_DIR:PATH=include

//ggml: use Kompute
GGML_KOMPUTE:BOOL=OFF

//ggml: enable lasx
GGML_LASX:BOOL=ON

//Location of library files
GGML_LIB_INSTALL_DIR:PATH=lib

//ggml: use LLAMAFILE
GGML_LLAMAFILE:BOOL=OFF

//ggml: enable lsx
GGML_LSX:BOOL=ON

//ggml: enable link time optimization
GGML_LTO:BOOL=OFF

//ggml: use Metal
GGML_METAL:BOOL=ON

//ggml: embed Metal library
GGML_METAL_EMBED_LIBRARY:BOOL=ON

//ggml: metal minimum macOS version
GGML_METAL_MACOSX_VERSION_MIN:STRING=

//ggml: disable Metal debugging
GGML_METAL_NDEBUG:BOOL=OFF

//ggml: compile Metal with -fno-fast-math
GGML_METAL_SHADER_DEBUG:BOOL=OFF

//ggml: metal standard version (-std flag)
GGML_METAL_STD:STRING=

//ggml: use bfloat if available
GGML_METAL_USE_BF16:BOOL=OFF

//ggml: use MUSA
GGML_MUSA:BOOL=OFF

//ggml: optimize the build for the current system
GGML_NATIVE:BOOL=ON

//ggml: use OpenCL
GGML_OPENCL:BOOL=OFF

//ggml: embed kernels
GGML_OPENCL_EMBED_KERNELS:BOOL=ON

//ggml: use OpenCL profiling (increases overhead)
GGML_OPENCL_PROFILING:BOOL=OFF

//ggml: use optimized kernels for Adreno
GGML_OPENCL_USE_ADRENO_KERNELS:BOOL=ON

//ggml: use OpenMP
GGML_OPENMP:BOOL=ON

//ggml: use RPC
GGML_RPC:BOOL=OFF

//ggml: enable rvv
GGML_RVV:BOOL=ON

//Path to a program.
GGML_SCCACHE_FOUND:FILEPATH=GGML_SCCACHE_FOUND-NOTFOUND

//ggml: max input copies for pipeline parallelism
GGML_SCHED_MAX_COPIES:STRING=4

//ggml: static link libraries
GGML_STATIC:BOOL=OFF

//ggml: use SYCL
GGML_SYCL:BOOL=OFF

//ggml: sycl device architecture
GGML_SYCL_DEVICE_ARCH:STRING=

//ggml: use 16 bit floats for sycl calculations
GGML_SYCL_F16:BOOL=OFF

//ggml: sycl target device
GGML_SYCL_TARGET:STRING=INTEL

//usr blas
GGML_USE_BLAS:BOOL=OFF

//ggml: use Vulkan
GGML_VULKAN:BOOL=OFF

//ggml: run Vulkan op checks
GGML_VULKAN_CHECK_RESULTS:BOOL=OFF

//ggml: enable Vulkan debug output
GGML_VULKAN_DEBUG:BOOL=OFF

//ggml: enable Vulkan memory debug output
GGML_VULKAN_MEMORY_DEBUG:BOOL=OFF

//ggml: enable Vulkan perf output
GGML_VULKAN_PERF:BOOL=OFF

//ggml: run Vulkan tests
GGML_VULKAN_RUN_TESTS:BOOL=OFF

//ggml: toolchain file for vulkan-shaders-gen
GGML_VULKAN_SHADERS_GEN_TOOLCHAIN:FILEPATH=

//ggml: enable Vulkan shader debug info
GGML_VULKAN_SHADER_DEBUG_INFO:BOOL=OFF

//ggml: enable Vulkan validation
GGML_VULKAN_VALIDATE:BOOL=OFF

//Path to a program.
GIT_EXE:FILEPATH=/usr/bin/git

//Git command line client
GIT_EXECUTABLE:FILEPATH=/usr/bin/git

//Path to a library.
MATH_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/lib/libm.tbd

//Path to a library.
METALKIT_FRAMEWORK:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/MetalKit.framework

//Path to a library.
METAL_FRAMEWORK:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Metal.framework

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=NOTFOUND

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=NOTFOUND

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=NOTFOUND

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=NOTFOUND

//Path to a library.
OpenMP_libomp_LIBRARY:FILEPATH=OpenMP_libomp_LIBRARY-NOTFOUND

//The directory containing a CMake configuration file for SDL2.
SDL2_DIR:PATH=/opt/homebrew/lib/cmake/SDL2

//sense-voice: enable all compiler warnings
SENSE_VOICE_ALL_WARNINGS:BOOL=ON

//sense-voice: enable all compiler warnings in 3rd party libs
SENSE_VOICE_ALL_WARNINGS_3RD_PARTY:BOOL=OFF

//whisper: build examples
SENSE_VOICE_BUILD_EXAMPLES:BOOL=ON

//sense-voice: build tests
SENSE_VOICE_BUILD_TESTS:BOOL=OFF

//sense-voice: use ccache if available
SENSE_VOICE_CCACHE:BOOL=ON

//sense voice: enable Core ML framework
SENSE_VOICE_COREML:BOOL=OFF

//sense voice: allow non-CoreML fallback
SENSE_VOICE_COREML_ALLOW_FALLBACK:BOOL=OFF

//sense-voice: enable -Werror flag
SENSE_VOICE_FATAL_WARNINGS:BOOL=OFF

//sense voice: support for OpenVINO
SENSE_VOICE_OPENVINO:BOOL=OFF

//sense-voice: enable address sanitizer
SENSE_VOICE_SANITIZE_ADDRESS:BOOL=OFF

//sense-voice: enable thread sanitizer
SENSE_VOICE_SANITIZE_THREAD:BOOL=OFF

//sense-voice: enable undefined sanitizer
SENSE_VOICE_SANITIZE_UNDEFINED:BOOL=OFF

//Value Computed by CMake
SenseVoice.cpp_BINARY_DIR:STATIC=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

//Value Computed by CMake
SenseVoice.cpp_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
SenseVoice.cpp_SOURCE_DIR:STATIC=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp

//Value Computed by CMake
ggml_BINARY_DIR:STATIC=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/sense-voice/csrc/third-party/ggml

//Value Computed by CMake
ggml_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
ggml_SOURCE_DIR:STATIC=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/sense-voice/csrc/third-party/ggml


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: BLAS_Accelerate_LIBRARY
BLAS_Accelerate_LIBRARY-ADVANCED:INTERNAL=1
//Have function dgemm_
BLAS_Accelerate_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER
CMAKE_ASM_COMPILER-ADVANCED:INTERNAL=1
CMAKE_ASM_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS
CMAKE_ASM_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_DEBUG
CMAKE_ASM_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_MINSIZEREL
CMAKE_ASM_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELEASE
CMAKE_ASM_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELWITHDEBINFO
CMAKE_ASM_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=29
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=12
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/Applications/CMake.app/Contents/share/cmake-3.29
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding BLAS
FIND_PACKAGE_MESSAGE_DETAILS_BLAS:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/Accelerate.framework][v()]
//Details about finding Git
FIND_PACKAGE_MESSAGE_DETAILS_Git:INTERNAL=[/usr/bin/git][v2.39.5 (Apple Git-154)()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//List of backends for cmake package
GGML_AVAILABLE_BACKENDS:INTERNAL=ggml-cpu;ggml-blas;ggml-metal
//Test GGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E
GGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E:INTERNAL=
//Test GGML_MACHINE_SUPPORTS_dotprod
GGML_MACHINE_SUPPORTS_dotprod:INTERNAL=1
//Result of TRY_COMPILE
GGML_MACHINE_SUPPORTS_dotprod_COMPILED:INTERNAL=TRUE
//Result of try_run()
GGML_MACHINE_SUPPORTS_dotprod_EXITCODE:INTERNAL=0
//Test GGML_MACHINE_SUPPORTS_i8mm
GGML_MACHINE_SUPPORTS_i8mm:INTERNAL=
//Result of TRY_COMPILE
GGML_MACHINE_SUPPORTS_i8mm_COMPILED:INTERNAL=TRUE
//Result of try_run()
GGML_MACHINE_SUPPORTS_i8mm_EXITCODE:INTERNAL=FAILED_TO_RUN
//Test GGML_MACHINE_SUPPORTS_sve
GGML_MACHINE_SUPPORTS_sve:INTERNAL=
//Result of TRY_COMPILE
GGML_MACHINE_SUPPORTS_sve_COMPILED:INTERNAL=TRUE
//Result of try_run()
GGML_MACHINE_SUPPORTS_sve_EXITCODE:INTERNAL=FAILED_TO_RUN
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp:INTERNAL=FALSE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_Xclang fopenmp:INTERNAL=FALSE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_libomp_LIBRARY
OpenMP_libomp_LIBRARY-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local

