/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -pthread -O3 -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=15.3 -Wl,-search_paths_first -Wl,-headerpad_max_install_names "CMakeFiles/sense-voice-zcr-main.dir/main.cc.o" -o ../../bin/sense-voice-zcr-main   -L/opt/homebrew/Cellar/sdl2/2.32.6/lib  -Wl,-rpath,/opt/homebrew/Cellar/sdl2/2.32.6/lib -Wl,-rpath,/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/lib ../../lib/libsense-voice-core.a ../../lib/libcommon.a ../../lib/libggml.dylib ../../lib/libggml-cpu.dylib ../../lib/libggml-blas.dylib ../../lib/libggml-metal.dylib ../../lib/libggml-base.dylib /opt/homebrew/Cellar/sdl2/2.32.6/lib/libSDL2.dylib
