# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: sense-voice/csrc/third-party/ggml/all
all: sense-voice/all
all: examples/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: sense-voice/csrc/third-party/ggml/preinstall
preinstall: sense-voice/preinstall
preinstall: examples/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: sense-voice/csrc/third-party/ggml/clean
clean: sense-voice/clean
clean: examples/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory examples

# Recursive "all" directory target.
examples/all: examples/CMakeFiles/common.dir/all
examples/all: examples/CMakeFiles/streamSDL.dir/all
examples/all: examples/quantize/all
examples/all: examples/zcr_main/all
examples/all: examples/stream/all
.PHONY : examples/all

# Recursive "preinstall" directory target.
examples/preinstall: examples/quantize/preinstall
examples/preinstall: examples/zcr_main/preinstall
examples/preinstall: examples/stream/preinstall
.PHONY : examples/preinstall

# Recursive "clean" directory target.
examples/clean: examples/CMakeFiles/common.dir/clean
examples/clean: examples/CMakeFiles/streamSDL.dir/clean
examples/clean: examples/quantize/clean
examples/clean: examples/zcr_main/clean
examples/clean: examples/stream/clean
.PHONY : examples/clean

#=============================================================================
# Directory level rules for directory examples/quantize

# Recursive "all" directory target.
examples/quantize/all: examples/quantize/CMakeFiles/sense-voice-quantize.dir/all
.PHONY : examples/quantize/all

# Recursive "preinstall" directory target.
examples/quantize/preinstall:
.PHONY : examples/quantize/preinstall

# Recursive "clean" directory target.
examples/quantize/clean: examples/quantize/CMakeFiles/sense-voice-quantize.dir/clean
.PHONY : examples/quantize/clean

#=============================================================================
# Directory level rules for directory examples/stream

# Recursive "all" directory target.
examples/stream/all: examples/stream/CMakeFiles/sense-voice-stream.dir/all
.PHONY : examples/stream/all

# Recursive "preinstall" directory target.
examples/stream/preinstall:
.PHONY : examples/stream/preinstall

# Recursive "clean" directory target.
examples/stream/clean: examples/stream/CMakeFiles/sense-voice-stream.dir/clean
.PHONY : examples/stream/clean

#=============================================================================
# Directory level rules for directory examples/zcr_main

# Recursive "all" directory target.
examples/zcr_main/all: examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all
.PHONY : examples/zcr_main/all

# Recursive "preinstall" directory target.
examples/zcr_main/preinstall:
.PHONY : examples/zcr_main/preinstall

# Recursive "clean" directory target.
examples/zcr_main/clean: examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/clean
.PHONY : examples/zcr_main/clean

#=============================================================================
# Directory level rules for directory sense-voice

# Recursive "all" directory target.
sense-voice/all: sense-voice/CMakeFiles/sense-voice-main.dir/all
sense-voice/all: sense-voice/csrc/all
.PHONY : sense-voice/all

# Recursive "preinstall" directory target.
sense-voice/preinstall: sense-voice/csrc/preinstall
.PHONY : sense-voice/preinstall

# Recursive "clean" directory target.
sense-voice/clean: sense-voice/CMakeFiles/sense-voice-main.dir/clean
sense-voice/clean: sense-voice/csrc/clean
.PHONY : sense-voice/clean

#=============================================================================
# Directory level rules for directory sense-voice/csrc

# Recursive "all" directory target.
sense-voice/csrc/all: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all
.PHONY : sense-voice/csrc/all

# Recursive "preinstall" directory target.
sense-voice/csrc/preinstall:
.PHONY : sense-voice/csrc/preinstall

# Recursive "clean" directory target.
sense-voice/csrc/clean: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/clean
.PHONY : sense-voice/csrc/clean

#=============================================================================
# Directory level rules for directory sense-voice/csrc/third-party/ggml

# Recursive "all" directory target.
sense-voice/csrc/third-party/ggml/all: sense-voice/csrc/third-party/ggml/src/all
.PHONY : sense-voice/csrc/third-party/ggml/all

# Recursive "preinstall" directory target.
sense-voice/csrc/third-party/ggml/preinstall: sense-voice/csrc/third-party/ggml/src/preinstall
.PHONY : sense-voice/csrc/third-party/ggml/preinstall

# Recursive "clean" directory target.
sense-voice/csrc/third-party/ggml/clean: sense-voice/csrc/third-party/ggml/src/clean
.PHONY : sense-voice/csrc/third-party/ggml/clean

#=============================================================================
# Directory level rules for directory sense-voice/csrc/third-party/ggml/src

# Recursive "all" directory target.
sense-voice/csrc/third-party/ggml/src/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
sense-voice/csrc/third-party/ggml/src/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
sense-voice/csrc/third-party/ggml/src/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
sense-voice/csrc/third-party/ggml/src/all: sense-voice/csrc/third-party/ggml/src/ggml-cpu/all
sense-voice/csrc/third-party/ggml/src/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/all
sense-voice/csrc/third-party/ggml/src/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/all
.PHONY : sense-voice/csrc/third-party/ggml/src/all

# Recursive "preinstall" directory target.
sense-voice/csrc/third-party/ggml/src/preinstall: sense-voice/csrc/third-party/ggml/src/ggml-cpu/preinstall
sense-voice/csrc/third-party/ggml/src/preinstall: sense-voice/csrc/third-party/ggml/src/ggml-blas/preinstall
sense-voice/csrc/third-party/ggml/src/preinstall: sense-voice/csrc/third-party/ggml/src/ggml-metal/preinstall
.PHONY : sense-voice/csrc/third-party/ggml/src/preinstall

# Recursive "clean" directory target.
sense-voice/csrc/third-party/ggml/src/clean: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/clean
sense-voice/csrc/third-party/ggml/src/clean: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/clean
sense-voice/csrc/third-party/ggml/src/clean: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/clean
sense-voice/csrc/third-party/ggml/src/clean: sense-voice/csrc/third-party/ggml/src/ggml-cpu/clean
sense-voice/csrc/third-party/ggml/src/clean: sense-voice/csrc/third-party/ggml/src/ggml-blas/clean
sense-voice/csrc/third-party/ggml/src/clean: sense-voice/csrc/third-party/ggml/src/ggml-metal/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/clean

#=============================================================================
# Directory level rules for directory sense-voice/csrc/third-party/ggml/src/ggml-blas

# Recursive "all" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-blas/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/all

# Recursive "preinstall" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-blas/preinstall:
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/preinstall

# Recursive "clean" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-blas/clean: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/clean

#=============================================================================
# Directory level rules for directory sense-voice/csrc/third-party/ggml/src/ggml-cpu

# Recursive "all" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-cpu/all:
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-cpu/all

# Recursive "preinstall" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-cpu/preinstall:
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-cpu/preinstall

# Recursive "clean" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-cpu/clean:
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-cpu/clean

#=============================================================================
# Directory level rules for directory sense-voice/csrc/third-party/ggml/src/ggml-metal

# Recursive "all" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-metal/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/all

# Recursive "preinstall" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-metal/preinstall:
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/preinstall

# Recursive "clean" directory target.
sense-voice/csrc/third-party/ggml/src/ggml-metal/clean: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/clean

#=============================================================================
# Target rules for target sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir

# All Build rule for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all:
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/depend
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=5,6,7,8,9,10,11,12 "Built target ggml-base"
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all

# Build rule for subdir invocation for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/rule

# Convenience name for target.
ggml-base: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/rule
.PHONY : ggml-base

# clean rule for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/clean:
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/clean

#=============================================================================
# Target rules for target sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir

# All Build rule for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/depend
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=3,4 "Built target ggml"
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all

# Build rule for subdir invocation for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/rule

# Convenience name for target.
ggml: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/rule
.PHONY : ggml

# clean rule for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/clean:
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/clean

#=============================================================================
# Target rules for target sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir

# All Build rule for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/depend
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=15,16,17,18,19,20,21,22,23 "Built target ggml-cpu"
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all

# Build rule for subdir invocation for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/rule

# Convenience name for target.
ggml-cpu: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/rule
.PHONY : ggml-cpu

# clean rule for target.
sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/clean:
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/build.make sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/clean

#=============================================================================
# Target rules for target sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir

# All Build rule for target.
sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/build.make sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/depend
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/build.make sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=13,14 "Built target ggml-blas"
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all

# Build rule for subdir invocation for target.
sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/rule

# Convenience name for target.
ggml-blas: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/rule
.PHONY : ggml-blas

# clean rule for target.
sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/clean:
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/build.make sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/clean

#=============================================================================
# Target rules for target sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir

# All Build rule for target.
sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/build.make sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/depend
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/build.make sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=24,25,26,27 "Built target ggml-metal"
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all

# Build rule for subdir invocation for target.
sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 12
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/rule

# Convenience name for target.
ggml-metal: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/rule
.PHONY : ggml-metal

# clean rule for target.
sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/clean:
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/build.make sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/clean
.PHONY : sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/clean

#=============================================================================
# Target rules for target sense-voice/CMakeFiles/sense-voice-main.dir

# All Build rule for target.
sense-voice/CMakeFiles/sense-voice-main.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
sense-voice/CMakeFiles/sense-voice-main.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
sense-voice/CMakeFiles/sense-voice-main.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
sense-voice/CMakeFiles/sense-voice-main.dir/all: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all
sense-voice/CMakeFiles/sense-voice-main.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
sense-voice/CMakeFiles/sense-voice-main.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
	$(MAKE) $(MAKESILENT) -f sense-voice/CMakeFiles/sense-voice-main.dir/build.make sense-voice/CMakeFiles/sense-voice-main.dir/depend
	$(MAKE) $(MAKESILENT) -f sense-voice/CMakeFiles/sense-voice-main.dir/build.make sense-voice/CMakeFiles/sense-voice-main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=36,37 "Built target sense-voice-main"
.PHONY : sense-voice/CMakeFiles/sense-voice-main.dir/all

# Build rule for subdir invocation for target.
sense-voice/CMakeFiles/sense-voice-main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/CMakeFiles/sense-voice-main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : sense-voice/CMakeFiles/sense-voice-main.dir/rule

# Convenience name for target.
sense-voice-main: sense-voice/CMakeFiles/sense-voice-main.dir/rule
.PHONY : sense-voice-main

# clean rule for target.
sense-voice/CMakeFiles/sense-voice-main.dir/clean:
	$(MAKE) $(MAKESILENT) -f sense-voice/CMakeFiles/sense-voice-main.dir/build.make sense-voice/CMakeFiles/sense-voice-main.dir/clean
.PHONY : sense-voice/CMakeFiles/sense-voice-main.dir/clean

#=============================================================================
# Target rules for target sense-voice/csrc/CMakeFiles/sense-voice-core.dir

# All Build rule for target.
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/depend
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=28,29,30,31,32,33,34,35 "Built target sense-voice-core"
.PHONY : sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all

# Build rule for subdir invocation for target.
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 33
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : sense-voice/csrc/CMakeFiles/sense-voice-core.dir/rule

# Convenience name for target.
sense-voice-core: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/rule
.PHONY : sense-voice-core

# clean rule for target.
sense-voice/csrc/CMakeFiles/sense-voice-core.dir/clean:
	$(MAKE) $(MAKESILENT) -f sense-voice/csrc/CMakeFiles/sense-voice-core.dir/build.make sense-voice/csrc/CMakeFiles/sense-voice-core.dir/clean
.PHONY : sense-voice/csrc/CMakeFiles/sense-voice-core.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/common.dir

# All Build rule for target.
examples/CMakeFiles/common.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
examples/CMakeFiles/common.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
examples/CMakeFiles/common.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
examples/CMakeFiles/common.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
examples/CMakeFiles/common.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/common.dir/build.make examples/CMakeFiles/common.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/common.dir/build.make examples/CMakeFiles/common.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=1,2 "Built target common"
.PHONY : examples/CMakeFiles/common.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/common.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 27
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/common.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/common.dir/rule

# Convenience name for target.
common: examples/CMakeFiles/common.dir/rule
.PHONY : common

# clean rule for target.
examples/CMakeFiles/common.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/common.dir/build.make examples/CMakeFiles/common.dir/clean
.PHONY : examples/CMakeFiles/common.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/streamSDL.dir

# All Build rule for target.
examples/CMakeFiles/streamSDL.dir/all:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/streamSDL.dir/build.make examples/CMakeFiles/streamSDL.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/streamSDL.dir/build.make examples/CMakeFiles/streamSDL.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=44,45 "Built target streamSDL"
.PHONY : examples/CMakeFiles/streamSDL.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/streamSDL.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/streamSDL.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/streamSDL.dir/rule

# Convenience name for target.
streamSDL: examples/CMakeFiles/streamSDL.dir/rule
.PHONY : streamSDL

# clean rule for target.
examples/CMakeFiles/streamSDL.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/streamSDL.dir/build.make examples/CMakeFiles/streamSDL.dir/clean
.PHONY : examples/CMakeFiles/streamSDL.dir/clean

#=============================================================================
# Target rules for target examples/quantize/CMakeFiles/sense-voice-quantize.dir

# All Build rule for target.
examples/quantize/CMakeFiles/sense-voice-quantize.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
examples/quantize/CMakeFiles/sense-voice-quantize.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
examples/quantize/CMakeFiles/sense-voice-quantize.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
examples/quantize/CMakeFiles/sense-voice-quantize.dir/all: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all
examples/quantize/CMakeFiles/sense-voice-quantize.dir/all: examples/CMakeFiles/common.dir/all
examples/quantize/CMakeFiles/sense-voice-quantize.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
examples/quantize/CMakeFiles/sense-voice-quantize.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
	$(MAKE) $(MAKESILENT) -f examples/quantize/CMakeFiles/sense-voice-quantize.dir/build.make examples/quantize/CMakeFiles/sense-voice-quantize.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/quantize/CMakeFiles/sense-voice-quantize.dir/build.make examples/quantize/CMakeFiles/sense-voice-quantize.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=38,39 "Built target sense-voice-quantize"
.PHONY : examples/quantize/CMakeFiles/sense-voice-quantize.dir/all

# Build rule for subdir invocation for target.
examples/quantize/CMakeFiles/sense-voice-quantize.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/quantize/CMakeFiles/sense-voice-quantize.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : examples/quantize/CMakeFiles/sense-voice-quantize.dir/rule

# Convenience name for target.
sense-voice-quantize: examples/quantize/CMakeFiles/sense-voice-quantize.dir/rule
.PHONY : sense-voice-quantize

# clean rule for target.
examples/quantize/CMakeFiles/sense-voice-quantize.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/quantize/CMakeFiles/sense-voice-quantize.dir/build.make examples/quantize/CMakeFiles/sense-voice-quantize.dir/clean
.PHONY : examples/quantize/CMakeFiles/sense-voice-quantize.dir/clean

#=============================================================================
# Target rules for target examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir

# All Build rule for target.
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all: examples/CMakeFiles/common.dir/all
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
	$(MAKE) $(MAKESILENT) -f examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/build.make examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/build.make examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=42,43 "Built target sense-voice-zcr-main"
.PHONY : examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all

# Build rule for subdir invocation for target.
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/rule

# Convenience name for target.
sense-voice-zcr-main: examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/rule
.PHONY : sense-voice-zcr-main

# clean rule for target.
examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/build.make examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/clean
.PHONY : examples/zcr_main/CMakeFiles/sense-voice-zcr-main.dir/clean

#=============================================================================
# Target rules for target examples/stream/CMakeFiles/sense-voice-stream.dir

# All Build rule for target.
examples/stream/CMakeFiles/sense-voice-stream.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-cpu.dir/all
examples/stream/CMakeFiles/sense-voice-stream.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-blas/CMakeFiles/ggml-blas.dir/all
examples/stream/CMakeFiles/sense-voice-stream.dir/all: sense-voice/csrc/third-party/ggml/src/ggml-metal/CMakeFiles/ggml-metal.dir/all
examples/stream/CMakeFiles/sense-voice-stream.dir/all: sense-voice/csrc/CMakeFiles/sense-voice-core.dir/all
examples/stream/CMakeFiles/sense-voice-stream.dir/all: examples/CMakeFiles/common.dir/all
examples/stream/CMakeFiles/sense-voice-stream.dir/all: examples/CMakeFiles/streamSDL.dir/all
examples/stream/CMakeFiles/sense-voice-stream.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml-base.dir/all
examples/stream/CMakeFiles/sense-voice-stream.dir/all: sense-voice/csrc/third-party/ggml/src/CMakeFiles/ggml.dir/all
	$(MAKE) $(MAKESILENT) -f examples/stream/CMakeFiles/sense-voice-stream.dir/build.make examples/stream/CMakeFiles/sense-voice-stream.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/stream/CMakeFiles/sense-voice-stream.dir/build.make examples/stream/CMakeFiles/sense-voice-stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles --progress-num=40,41 "Built target sense-voice-stream"
.PHONY : examples/stream/CMakeFiles/sense-voice-stream.dir/all

# Build rule for subdir invocation for target.
examples/stream/CMakeFiles/sense-voice-stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 39
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/stream/CMakeFiles/sense-voice-stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/english_learn/SenseVoice.cpp/build/CMakeFiles 0
.PHONY : examples/stream/CMakeFiles/sense-voice-stream.dir/rule

# Convenience name for target.
sense-voice-stream: examples/stream/CMakeFiles/sense-voice-stream.dir/rule
.PHONY : sense-voice-stream

# clean rule for target.
examples/stream/CMakeFiles/sense-voice-stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/stream/CMakeFiles/sense-voice-stream.dir/build.make examples/stream/CMakeFiles/sense-voice-stream.dir/clean
.PHONY : examples/stream/CMakeFiles/sense-voice-stream.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

