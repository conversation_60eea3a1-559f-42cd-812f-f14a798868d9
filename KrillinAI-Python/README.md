# KrillinAI Python版本

## 项目简介

这是KrillinAI的Python重写版本，一个极简的AI视频翻译和配音工具。

## 主要功能

🎯 **一键启动**: 无需复杂环境配置，自动依赖安装，开箱即用
📥 **视频获取**: 支持yt-dlp下载或本地文件上传  
📜 **精准识别**: 基于Whisper的高精度语音识别
🧠 **智能分割**: 使用LLM进行字幕分割和对齐
🔄 **术语替换**: 一键替换专业词汇
🌍 **专业翻译**: 基于LLM的段落级翻译，保持语义连贯
🎙️ **声音克隆**: 提供CosyVoice精选音色或自定义声音克隆
🎬 **视频合成**: 自动处理横屏和竖屏视频及字幕布局

## 技术架构

### 后端架构
- **Web框架**: FastAPI
- **异步处理**: asyncio + Celery
- **配置管理**: Pydantic Settings
- **日志系统**: loguru
- **数据存储**: SQLite + Redis

### 核心模块
- **语音识别**: OpenAI Whisper, FasterWhisper, WhisperKit
- **大语言模型**: OpenAI, DeepSeek, 通义千问等
- **云服务**: 阿里云ASR/TTS/OSS
- **视频处理**: FFmpeg
- **字幕处理**: SRT/ASS格式支持

## 项目结构

```
KrillinAI-Python/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py         # 配置管理
│   │   └── config.toml         # 配置文件
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── subtitle.py     # 字幕相关API
│   │   │   └── task.py         # 任务管理API
│   │   └── dependencies.py     # 依赖注入
│   ├── core/
│   │   ├── __init__.py
│   │   ├── audio_processor.py  # 音频处理
│   │   ├── subtitle_service.py # 字幕服务
│   │   ├── translation.py      # 翻译服务
│   │   ├── tts_service.py      # 语音合成
│   │   └── video_processor.py  # 视频处理
│   ├── models/
│   │   ├── __init__.py
│   │   ├── task.py             # 任务模型
│   │   └── subtitle.py         # 字幕模型
│   ├── services/
│   │   ├── __init__.py
│   │   ├── whisper_service.py  # Whisper服务
│   │   ├── llm_service.py      # LLM服务
│   │   └── aliyun_service.py   # 阿里云服务
│   └── utils/
│       ├── __init__.py
│       ├── file_utils.py       # 文件工具
│       ├── video_utils.py      # 视频工具
│       └── logger.py           # 日志工具
├── static/                     # 静态文件
├── templates/                  # 模板文件
├── tests/                      # 测试文件
├── requirements.txt            # Python依赖
├── docker-compose.yml          # Docker配置
└── README.md                   # 项目文档
```

## 支持的语音识别服务

| 服务来源 | 支持平台 | 模型选项 | 本地/云端 | 备注 |
|---------|---------|---------|----------|------|
| **OpenAI Whisper** | 全平台 | - | 云端 | 快速有效 |
| **FasterWhisper** | Windows/Linux | tiny/medium/large-v2 | 本地 | 更快，无云服务费用 |
| **WhisperKit** | macOS (M系列) | large-v2 | 本地 | Apple芯片原生优化 |
| **WhisperCpp** | 全平台 | large-v2 | 本地 | 支持所有平台 |
| **阿里云ASR** | 全平台 | - | 云端 | 避免国内网络问题 |

## 大语言模型支持

✅ 兼容所有符合**OpenAI API规范**的云端/本地大语言模型服务，包括但不限于：
- OpenAI
- DeepSeek  
- 通义千问
- 本地部署的开源模型
- 其他兼容OpenAI格式的API服务

## 语言支持

**输入语言支持**: 中文、英文、日文、德文、土耳其文、韩文、俄文、马来文（持续增加中）

**翻译语言支持**: 英文、中文、俄文、西班牙文、法文等101种语言

## 快速开始

### 环境要求
- Python 3.8+
- FFmpeg
- Redis (可选，用于任务队列)

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/your-repo/KrillinAI-Python.git
cd KrillinAI-Python
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置文件
```bash
cp app/config/config-example.toml app/config/config.toml
# 编辑配置文件，填入你的API密钥
```

4. 启动服务
```bash
python -m app.main
```

5. 访问界面
打开浏览器访问 `http://127.0.0.1:8888`

### Docker部署

```bash
docker-compose up -d
```

## 配置说明

最快最便捷的配置方法：
- 将 `transcription_provider` 和 `llm_provider` 都设置为 `openai`
- 只需要在配置中填入 `openai.api_key`

使用本地语音识别模型的配置方法：
- 设置 `transcription_provider` 为 `fasterwhisper`
- 设置 `llm_provider` 为 `openai`
- 填入 `openai.api_key` 和 `local_model.fasterwhisper`

## 开发指南

### 添加新的语音识别服务
1. 在 `app/services/` 下创建新的服务文件
2. 实现 `BaseTranscriptionService` 接口
3. 在配置中注册新服务

### 添加新的LLM服务
1. 在 `app/services/` 下创建新的LLM服务文件
2. 实现 `BaseLLMService` 接口
3. 在配置中注册新服务

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系我们

- QQ群: 754069680
- Bilibili: [KrillinAI](https://space.bilibili.com/242124650)
- Twitter: [@KrillinAI](https://x.com/KrillinAI)
