"""
翻译服务 - 对应Go版本的翻译功能
"""

import os
from pathlib import Path
from app.models.task import TaskStepParam
from app.utils.logger import get_task_logger
from app.config.settings import get_settings


class TranslationService:
    """翻译服务"""
    
    def __init__(self):
        self.settings = get_settings()
    
    async def process(self, step_param: TaskStepParam):
        """
        处理字幕翻译
        """
        task_logger = get_task_logger(step_param.task_id)
        
        if step_param.target_language == "none" or step_param.target_language == step_param.origin_language:
            task_logger.info("跳过翻译步骤")
            return
        
        # 查找原始字幕文件
        output_dir = Path(step_param.task_dir) / "output"
        original_srt = output_dir / f"{step_param.task_id}_original.srt"
        
        if not original_srt.exists():
            raise FileNotFoundError("未找到原始字幕文件")
        
        # 根据配置选择翻译服务
        if self.settings.llm_provider == "openai":
            await self._translate_with_openai(step_param, original_srt)
        elif self.settings.llm_provider == "deepseek":
            await self._translate_with_deepseek(step_param, original_srt)
        else:
            # 创建占位符翻译
            await self._create_placeholder_translation(step_param, original_srt)
    
    async def _translate_with_openai(self, step_param: TaskStepParam, srt_file: Path):
        """使用OpenAI进行翻译"""
        task_logger = get_task_logger(step_param.task_id)
        
        try:
            import openai
            
            client = openai.OpenAI(
                api_key=self.settings.openai.api_key,
                base_url=self.settings.openai.base_url
            )
            
            # 读取原始字幕
            with open(srt_file, "r", encoding="utf-8") as f:
                srt_content = f.read()
            
            # 构建翻译提示
            prompt = f"""
请将以下SRT字幕文件翻译为{self._get_language_name(step_param.target_language)}，保持SRT格式不变，只翻译文本内容：

{srt_content}
"""
            
            response = client.chat.completions.create(
                model=self.settings.openai.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的字幕翻译助手，请准确翻译字幕内容，保持时间轴不变。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            translated_content = response.choices[0].message.content
            
            # 保存翻译后的字幕
            translated_srt = srt_file.parent / f"{step_param.task_id}_translated.srt"
            with open(translated_srt, "w", encoding="utf-8") as f:
                f.write(translated_content)
            
            task_logger.info(f"OpenAI翻译完成: {translated_srt.name}")
            
        except Exception as e:
            task_logger.error(f"OpenAI翻译失败: {e}")
            await self._create_placeholder_translation(step_param, srt_file)
    
    async def _translate_with_deepseek(self, step_param: TaskStepParam, srt_file: Path):
        """使用DeepSeek进行翻译"""
        task_logger = get_task_logger(step_param.task_id)
        
        # 类似OpenAI的实现，使用DeepSeek API
        task_logger.info("DeepSeek翻译暂未实现，使用占位符")
        await self._create_placeholder_translation(step_param, srt_file)
    
    async def _create_placeholder_translation(self, step_param: TaskStepParam, srt_file: Path):
        """创建占位符翻译"""
        task_logger = get_task_logger(step_param.task_id)
        
        # 读取原始字幕
        with open(srt_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 简单的占位符翻译（在每行前加上[翻译]标记）
        lines = content.split('\n')
        translated_lines = []
        
        for line in lines:
            if line.strip() and not line.strip().isdigit() and '-->' not in line:
                # 这是字幕文本行
                translated_lines.append(f"[翻译] {line}")
            else:
                # 这是序号或时间轴行
                translated_lines.append(line)
        
        translated_content = '\n'.join(translated_lines)
        
        # 保存翻译后的字幕
        translated_srt = srt_file.parent / f"{step_param.task_id}_translated.srt"
        with open(translated_srt, "w", encoding="utf-8") as f:
            f.write(translated_content)
        
        task_logger.info(f"占位符翻译完成: {translated_srt.name}")
    
    def _get_language_name(self, language_code: str) -> str:
        """获取语言名称"""
        language_map = {
            "zh": "中文",
            "en": "英文",
            "ja": "日文",
            "ko": "韩文",
            "fr": "法文",
            "de": "德文",
            "es": "西班牙文",
            "ru": "俄文"
        }
        return language_map.get(language_code, language_code)
