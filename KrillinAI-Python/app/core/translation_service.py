"""
翻译服务 - 对应Go版本的翻译功能
优化版本：使用Go版本的prompt设计，支持分块翻译和并发处理
"""

import os
import re
import asyncio
from pathlib import Path
from typing import List, Dict, Tuple
from app.models.task import TaskStepParam
from app.utils.logger import get_task_logger
from app.config.settings import get_settings


class TranslationService:
    """翻译服务"""

    def __init__(self):
        self.settings = get_settings()

        # Go版本的翻译prompt模板
        self.split_text_prompt = """你是一个语言处理专家，专注于自然语言处理和翻译任务。按照以下步骤和要求，以最大程度实现准确和高质量翻译：

1. 将原句翻译为{target_language}，确保译文流畅、自然，达到专业翻译水平。
2. 严格依据标点符号（逗号、句号、问号等）将内容拆分成单独的句子，并依据以下规则确保拆分粒度合理：
   - 每个句子在保证句意完整的情况下尽可能短，长度尽量不得超过15个字。
   - 可以根据连词（例如 "and", "but", "which", "when", "so", "所以", "但是", "因此", "考虑到" 等）进一步拆分句子，避免语句太长。
3. 对每个拆分的句子分别翻译，确保不遗漏或修改任何字词。
4. 将每对翻译后的句子与原句用独立编号表示，并分别以方括号[]包裹内容。
5. 输出的翻译与原文应保持对应，严格按照原文顺序呈现，不得有错位，且原文尽可能使用原文。
6. 不管内容是正式还是非正式，都要翻译。

翻译输出应采用如下格式：
**正常翻译的示例（注意每块3部分，每个部分都独占一行，空格分块）**：
1
[翻译后的句子1]
[原句子1]

2
[翻译后的句子2]
[原句子2]

**无文本需要翻译的输出示例**：
[无文本]

确保高效、精确地完成上述翻译任务，输入内容如下：
"""

        # 带语气词过滤的prompt
        self.split_text_prompt_with_modal_filter = """你是一个语言处理专家，专注于自然语言处理和翻译任务。按照以下步骤和要求，以最大程度实现准确和高质量翻译：

1. 将原句翻译为{target_language}，确保译文流畅、自然，达到专业翻译水平。
2. 严格依据标点符号（逗号、句号、问号等）将内容拆分成单独的句子，并依据以下规则确保拆分粒度合理：
   - 每个句子在保证句意完整的情况下尽可能短，长度尽量不得超过15个字。
   - 可以根据连词（例如 "and", "but", "which", "when", "so", "所以", "但是", "因此", "考虑到" 等）进一步拆分句子，避免语句太长。
3. 对每个拆分的句子分别翻译，确保不遗漏或修改任何字词。
4. 将每对翻译后的句子与原句用独立编号表示，并分别以方括号[]包裹内容。
5. 输出的翻译与原文应保持对应，严格按照原文顺序呈现，不得有错位，且原文尽可能使用原文。
6. 忽略文本中的语气词，比如"Oh" "Ah" "Wow"等等。
7. 不管内容是正式还是非正式，都要翻译。

翻译输出应采用如下格式：
**正常翻译的示例（注意每块3部分，每个部分都独占一行，空格分块）**：
1
[翻译后的句子1]
[原句子1]

2
[翻译后的句子2]
[原句子2]

**无文本需要翻译的输出示例**：
[无文本]

确保高效、精确地完成上述翻译任务，输入内容如下：
"""

    async def process(self, step_param: TaskStepParam):
        """
        处理字幕翻译
        """
        task_logger = get_task_logger(step_param.task_id)

        if (
            step_param.target_language == "none"
            or step_param.target_language == step_param.origin_language
        ):
            task_logger.info("跳过翻译步骤")
            return

        # 查找原始字幕文件
        output_dir = Path(step_param.task_dir) / "output"
        original_srt = output_dir / f"{step_param.task_id}_original.srt"

        if not original_srt.exists():
            raise FileNotFoundError("未找到原始字幕文件")

        # 根据配置选择翻译服务
        if self.settings.llm_provider == "openai":
            await self._translate_with_openai(step_param, original_srt)
        elif self.settings.llm_provider == "deepseek":
            await self._translate_with_deepseek(step_param, original_srt)
        else:
            # 创建占位符翻译
            await self._create_placeholder_translation(step_param, original_srt)

    async def _translate_with_openai(self, step_param: TaskStepParam, srt_file: Path):
        """使用OpenAI进行翻译"""
        task_logger = get_task_logger(step_param.task_id)

        try:
            import openai

            client = openai.OpenAI(
                api_key=self.settings.openai.api_key,
                base_url=self.settings.openai.base_url,
            )

            # 读取原始字幕
            with open(srt_file, "r", encoding="utf-8") as f:
                srt_content = f.read()

            # 构建翻译提示
            prompt = f"""
请将以下SRT字幕文件翻译为{self._get_language_name(step_param.target_language)}，保持SRT格式不变，只翻译文本内容：

{srt_content}
"""

            response = client.chat.completions.create(
                model=self.settings.openai.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的字幕翻译助手，请准确翻译字幕内容，保持时间轴不变。",
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.3,
            )

            translated_content = response.choices[0].message.content

            # 保存翻译后的字幕
            translated_srt = srt_file.parent / f"{step_param.task_id}_translated.srt"
            with open(translated_srt, "w", encoding="utf-8") as f:
                f.write(translated_content)

            task_logger.info(f"OpenAI翻译完成: {translated_srt.name}")

        except Exception as e:
            task_logger.error(f"OpenAI翻译失败: {e}")
            await self._create_placeholder_translation(step_param, srt_file)

    async def _translate_with_deepseek(self, step_param: TaskStepParam, srt_file: Path):
        """使用DeepSeek进行翻译"""
        task_logger = get_task_logger(step_param.task_id)

        # 类似OpenAI的实现，使用DeepSeek API
        task_logger.info("DeepSeek翻译暂未实现，使用占位符")
        await self._create_placeholder_translation(step_param, srt_file)

    async def _create_placeholder_translation(
        self, step_param: TaskStepParam, srt_file: Path
    ):
        """创建占位符翻译"""
        task_logger = get_task_logger(step_param.task_id)

        # 读取原始字幕
        with open(srt_file, "r", encoding="utf-8") as f:
            content = f.read()

        # 简单的占位符翻译（在每行前加上[翻译]标记）
        lines = content.split("\n")
        translated_lines = []

        for line in lines:
            if line.strip() and not line.strip().isdigit() and "-->" not in line:
                # 这是字幕文本行
                translated_lines.append(f"[翻译] {line}")
            else:
                # 这是序号或时间轴行
                translated_lines.append(line)

        translated_content = "\n".join(translated_lines)

        # 保存翻译后的字幕
        translated_srt = srt_file.parent / f"{step_param.task_id}_translated.srt"
        with open(translated_srt, "w", encoding="utf-8") as f:
            f.write(translated_content)

        task_logger.info(f"占位符翻译完成: {translated_srt.name}")

    def _get_language_name(self, language_code: str) -> str:
        """获取语言名称"""
        language_map = {
            "zh": "中文",
            "en": "英文",
            "ja": "日文",
            "ko": "韩文",
            "fr": "法文",
            "de": "德文",
            "es": "西班牙文",
            "ru": "俄文",
        }
        return language_map.get(language_code, language_code)
