"""
音频处理器 - 对应Go版本的audioToSubtitle功能
"""

import os
import asyncio
from pathlib import Path
from app.models.task import TaskStepParam
from app.utils.logger import get_task_logger
from app.config.settings import get_settings


class AudioProcessor:
    """音频处理器"""
    
    def __init__(self):
        self.settings = get_settings()
    
    async def process(self, step_param: TaskStepParam):
        """
        处理音频转字幕
        对应Go版本的audioToSubtitle方法
        """
        task_logger = get_task_logger(step_param.task_id)
        
        if not step_param.input_file_path or not os.path.exists(step_param.input_file_path):
            raise FileNotFoundError("音频文件不存在")
        
        # 根据配置选择转录服务
        if self.settings.transcription_provider == "openai":
            await self._transcribe_with_openai(step_param)
        elif self.settings.transcription_provider == "faster_whisper":
            await self._transcribe_with_faster_whisper(step_param)
        elif self.settings.transcription_provider == "aliyun":
            await self._transcribe_with_aliyun(step_param)
        else:
            # 默认使用命令行Whisper
            await self._transcribe_with_whisper_cli(step_param)
    
    async def _transcribe_with_whisper_cli(self, step_param: TaskStepParam):
        """使用命令行Whisper进行转录"""
        task_logger = get_task_logger(step_param.task_id)
        
        output_dir = Path(step_param.task_dir) / "output"
        output_dir.mkdir(exist_ok=True)
        
        # 构建Whisper命令
        cmd = [
            "whisper",
            step_param.input_file_path,
            "--model", "large",
            "--language", step_param.origin_language if step_param.origin_language != "auto" else "English",
            "--output_format", "srt",
            "--output_dir", str(output_dir),
            "--verbose", "False"
        ]
        
        task_logger.info(f"开始语音识别: {Path(step_param.input_file_path).name}")
        
        # 执行Whisper命令
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            error_msg = stderr.decode('utf-8') if stderr else "语音识别失败"
            task_logger.error(f"语音识别失败: {error_msg}")
            raise RuntimeError(f"语音识别失败: {error_msg}")
        
        # 查找生成的字幕文件
        input_name = Path(step_param.input_file_path).stem
        srt_files = list(output_dir.glob(f"{input_name}*.srt"))
        
        if not srt_files:
            raise RuntimeError("语音识别完成但未找到字幕文件")
        
        # 重命名为标准格式
        original_srt = srt_files[0]
        standard_srt = output_dir / f"{step_param.task_id}_original.srt"
        
        if original_srt != standard_srt:
            original_srt.rename(standard_srt)
        
        task_logger.info(f"语音识别完成: {standard_srt.name}")
    
    async def _transcribe_with_openai(self, step_param: TaskStepParam):
        """使用OpenAI Whisper API进行转录"""
        task_logger = get_task_logger(step_param.task_id)
        
        try:
            import openai
            
            # 配置OpenAI客户端
            client = openai.OpenAI(
                api_key=self.settings.openai.api_key,
                base_url=self.settings.openai.base_url
            )
            
            # 读取音频文件
            with open(step_param.input_file_path, "rb") as audio_file:
                transcript = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="srt",
                    language=step_param.origin_language if step_param.origin_language != "auto" else None
                )
            
            # 保存字幕文件
            output_dir = Path(step_param.task_dir) / "output"
            output_dir.mkdir(exist_ok=True)
            
            srt_file = output_dir / f"{step_param.task_id}_original.srt"
            with open(srt_file, "w", encoding="utf-8") as f:
                f.write(transcript)
            
            task_logger.info(f"OpenAI语音识别完成: {srt_file.name}")
            
        except Exception as e:
            task_logger.error(f"OpenAI语音识别失败: {e}")
            # 回退到命令行Whisper
            await self._transcribe_with_whisper_cli(step_param)
    
    async def _transcribe_with_faster_whisper(self, step_param: TaskStepParam):
        """使用FasterWhisper进行转录"""
        task_logger = get_task_logger(step_param.task_id)
        
        try:
            from faster_whisper import WhisperModel
            
            # 加载模型
            model = WhisperModel(
                self.settings.local_model.faster_whisper_model_size,
                device=self.settings.local_model.faster_whisper_device,
                compute_type=self.settings.local_model.faster_whisper_compute_type
            )
            
            # 转录音频
            segments, info = model.transcribe(
                step_param.input_file_path,
                language=step_param.origin_language if step_param.origin_language != "auto" else None
            )
            
            # 转换为SRT格式
            srt_content = self._segments_to_srt(segments)
            
            # 保存字幕文件
            output_dir = Path(step_param.task_dir) / "output"
            output_dir.mkdir(exist_ok=True)
            
            srt_file = output_dir / f"{step_param.task_id}_original.srt"
            with open(srt_file, "w", encoding="utf-8") as f:
                f.write(srt_content)
            
            task_logger.info(f"FasterWhisper语音识别完成: {srt_file.name}")
            
        except Exception as e:
            task_logger.error(f"FasterWhisper语音识别失败: {e}")
            # 回退到命令行Whisper
            await self._transcribe_with_whisper_cli(step_param)
    
    async def _transcribe_with_aliyun(self, step_param: TaskStepParam):
        """使用阿里云ASR进行转录"""
        task_logger = get_task_logger(step_param.task_id)
        
        # 这里可以实现阿里云ASR的调用
        # 目前回退到命令行Whisper
        task_logger.info("阿里云ASR暂未实现，使用Whisper替代")
        await self._transcribe_with_whisper_cli(step_param)
    
    def _segments_to_srt(self, segments) -> str:
        """将segments转换为SRT格式"""
        srt_content = ""
        
        for i, segment in enumerate(segments, 1):
            start_time = self._format_timestamp(segment.start)
            end_time = self._format_timestamp(segment.end)
            text = segment.text.strip()
            
            srt_content += f"{i}\n"
            srt_content += f"{start_time} --> {end_time}\n"
            srt_content += f"{text}\n\n"
        
        return srt_content
    
    def _format_timestamp(self, seconds: float) -> str:
        """格式化时间戳为SRT格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
