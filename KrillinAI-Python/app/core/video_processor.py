"""
视频处理器 - 对应Go版本的embedSubtitles功能
"""

from pathlib import Path
from app.models.task import TaskStepParam, SubtitleResultType
from app.utils.logger import get_task_logger


class VideoProcessor:
    """视频处理器"""
    
    async def process(self, step_param: TaskStepParam):
        """
        处理视频字幕嵌入
        """
        task_logger = get_task_logger(step_param.task_id)
        
        # 生成最终的字幕文件
        await self._generate_final_subtitles(step_param)
        
        task_logger.info("视频处理完成")
    
    async def _generate_final_subtitles(self, step_param: TaskStepParam):
        """生成最终的字幕文件"""
        task_logger = get_task_logger(step_param.task_id)
        
        output_dir = Path(step_param.task_dir) / "output"
        original_srt = output_dir / f"{step_param.task_id}_original.srt"
        translated_srt = output_dir / f"{step_param.task_id}_translated.srt"
        
        # 根据字幕结果类型生成不同的字幕文件
        if step_param.subtitle_result_type == SubtitleResultType.ORIGIN_ONLY:
            # 仅原文
            if original_srt.exists():
                final_srt = output_dir / f"{step_param.task_id}_final.srt"
                original_srt.rename(final_srt)
                task_logger.info(f"生成原文字幕: {final_srt.name}")
        
        elif step_param.subtitle_result_type == SubtitleResultType.TARGET_ONLY:
            # 仅译文
            if translated_srt.exists():
                final_srt = output_dir / f"{step_param.task_id}_final.srt"
                translated_srt.rename(final_srt)
                task_logger.info(f"生成译文字幕: {final_srt.name}")
        
        else:
            # 双语字幕
            await self._create_bilingual_subtitles(step_param)
    
    async def _create_bilingual_subtitles(self, step_param: TaskStepParam):
        """创建双语字幕"""
        task_logger = get_task_logger(step_param.task_id)
        
        output_dir = Path(step_param.task_dir) / "output"
        original_srt = output_dir / f"{step_param.task_id}_original.srt"
        translated_srt = output_dir / f"{step_param.task_id}_translated.srt"
        
        if not original_srt.exists() or not translated_srt.exists():
            task_logger.warning("缺少字幕文件，无法生成双语字幕")
            return
        
        # 读取原文和译文字幕
        with open(original_srt, "r", encoding="utf-8") as f:
            original_content = f.read()
        
        with open(translated_srt, "r", encoding="utf-8") as f:
            translated_content = f.read()
        
        # 解析字幕块
        original_blocks = self._parse_srt_blocks(original_content)
        translated_blocks = self._parse_srt_blocks(translated_content)
        
        # 合并双语字幕
        bilingual_content = self._merge_bilingual_blocks(
            original_blocks, 
            translated_blocks, 
            step_param.subtitle_result_type
        )
        
        # 保存双语字幕
        bilingual_srt = output_dir / f"{step_param.task_id}_bilingual.srt"
        with open(bilingual_srt, "w", encoding="utf-8") as f:
            f.write(bilingual_content)
        
        task_logger.info(f"生成双语字幕: {bilingual_srt.name}")
    
    def _parse_srt_blocks(self, content: str) -> list:
        """解析SRT字幕块"""
        blocks = []
        for block in content.strip().split('\n\n'):
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                blocks.append({
                    'index': lines[0],
                    'timestamp': lines[1],
                    'text': '\n'.join(lines[2:])
                })
        return blocks
    
    def _merge_bilingual_blocks(self, original_blocks: list, translated_blocks: list, result_type: SubtitleResultType) -> str:
        """合并双语字幕块"""
        merged_content = ""
        min_blocks = min(len(original_blocks), len(translated_blocks))
        
        for i in range(min_blocks):
            original = original_blocks[i]
            translated = translated_blocks[i]
            
            # 使用原文的时间轴
            merged_content += f"{original['index']}\n"
            merged_content += f"{original['timestamp']}\n"
            
            # 根据类型决定文本顺序
            if result_type == SubtitleResultType.BILINGUAL_TRANSLATION_ON_TOP:
                merged_content += f"{translated['text']}\n"
                merged_content += f"{original['text']}\n\n"
            else:  # BILINGUAL_TRANSLATION_ON_BOTTOM
                merged_content += f"{original['text']}\n"
                merged_content += f"{translated['text']}\n\n"
        
        return merged_content
