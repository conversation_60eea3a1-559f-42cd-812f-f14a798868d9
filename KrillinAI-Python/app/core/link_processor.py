"""
链接处理器 - 对应Go版本的linkToFile功能
"""

import os
import asyncio
from pathlib import Path
from app.models.task import TaskStepParam
from app.utils.logger import get_task_logger
from app.utils.video_utils import is_youtube_url, is_bilibili_url


class LinkProcessor:
    """链接处理器"""
    
    async def process(self, step_param: TaskStepParam):
        """
        处理链接到文件的转换
        对应Go版本的linkToFile方法
        """
        task_logger = get_task_logger(step_param.task_id)
        
        if step_param.link:
            # 处理在线视频链接
            await self._download_from_url(step_param)
        elif step_param.input_file_path:
            # 处理本地文件
            await self._process_local_file(step_param)
        else:
            raise ValueError("没有提供有效的输入源")
    
    async def _download_from_url(self, step_param: TaskStepParam):
        """从URL下载视频"""
        task_logger = get_task_logger(step_param.task_id)
        
        output_dir = Path(step_param.task_dir) / "temp"
        output_dir.mkdir(exist_ok=True)
        
        # 构建yt-dlp命令
        output_template = str(output_dir / "%(title)s.%(ext)s")
        
        cmd = [
            "yt-dlp",
            "--extract-flat", "false",
            "--write-info-json",
            "--format", "bestaudio",  # 只下载音频用于字幕生成
            "--extract-audio",
            "--audio-format", "mp3",
            "--output", output_template,
            step_param.link
        ]
        
        task_logger.info(f"开始下载: {step_param.link}")
        
        # 执行下载命令
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            error_msg = stderr.decode('utf-8') if stderr else "下载失败"
            task_logger.error(f"下载失败: {error_msg}")
            raise RuntimeError(f"下载失败: {error_msg}")
        
        # 查找下载的文件
        downloaded_files = list(output_dir.glob("*.mp3"))
        if not downloaded_files:
            raise RuntimeError("下载完成但未找到音频文件")
        
        # 更新步骤参数
        step_param.input_file_path = str(downloaded_files[0])
        task_logger.info(f"下载完成: {step_param.input_file_path}")
    
    async def _process_local_file(self, step_param: TaskStepParam):
        """处理本地文件"""
        task_logger = get_task_logger(step_param.task_id)
        
        if not os.path.exists(step_param.input_file_path):
            raise FileNotFoundError(f"文件不存在: {step_param.input_file_path}")
        
        # 如果是视频文件，提取音频
        file_ext = Path(step_param.input_file_path).suffix.lower()
        
        if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']:
            await self._extract_audio_from_video(step_param)
        elif file_ext in ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a']:
            # 音频文件直接使用
            task_logger.info(f"使用音频文件: {step_param.input_file_path}")
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
    
    async def _extract_audio_from_video(self, step_param: TaskStepParam):
        """从视频中提取音频"""
        task_logger = get_task_logger(step_param.task_id)
        
        output_dir = Path(step_param.task_dir) / "temp"
        output_dir.mkdir(exist_ok=True)
        
        input_file = Path(step_param.input_file_path)
        output_file = output_dir / f"{input_file.stem}.mp3"
        
        # 使用FFmpeg提取音频
        cmd = [
            "ffmpeg",
            "-i", step_param.input_file_path,
            "-vn",  # 不包含视频
            "-acodec", "mp3",
            "-ab", "192k",
            "-ar", "16000",  # Whisper推荐的采样率
            "-y",  # 覆盖输出文件
            str(output_file)
        ]
        
        task_logger.info(f"提取音频: {input_file.name} -> {output_file.name}")
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            error_msg = stderr.decode('utf-8') if stderr else "音频提取失败"
            task_logger.error(f"音频提取失败: {error_msg}")
            raise RuntimeError(f"音频提取失败: {error_msg}")
        
        # 更新步骤参数
        step_param.input_file_path = str(output_file)
        task_logger.info(f"音频提取完成: {output_file}")
