"""
字幕服务核心模块 - 对应Go版本的subtitle_service.go
"""

import os
import asyncio
import traceback
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from app.models.task import (
    TaskRequest, TaskResponse, TaskStatus, TaskType, TaskProgress,
    TaskStepParam, SubtitleResultType, task_storage
)
from app.utils.logger import get_logger, get_task_logger
from app.utils.file_utils import generate_random_string, create_task_directory
from app.utils.video_utils import get_youtube_id, get_bilibili_video_id
from app.core.link_processor import LinkProcessor
from app.core.audio_processor import AudioProcessor
from app.core.translation_service import TranslationService
from app.core.tts_service import TTSService
from app.core.video_processor import VideoProcessor
from app.config.settings import get_settings

logger = get_logger()


class SubtitleService:
    """字幕服务类"""
    
    def __init__(self):
        self.settings = get_settings()
        self.link_processor = LinkProcessor()
        self.audio_processor = AudioProcessor()
        self.translation_service = TranslationService()
        self.tts_service = TTSService()
        self.video_processor = VideoProcessor()
    
    async def start_subtitle_task(self, request: TaskRequest) -> TaskResponse:
        """
        启动字幕任务
        对应Go版本的StartSubtitleTask方法
        """
        # 校验链接
        if request.url:
            if "youtube.com" in request.url:
                video_id = get_youtube_id(request.url)
                if not video_id:
                    raise ValueError("YouTube链接不合法")
            elif "bilibili.com" in request.url:
                video_id = get_bilibili_video_id(request.url)
                if not video_id:
                    raise ValueError("Bilibili链接不合法")
        
        # 生成任务ID
        task_id = generate_random_string(8)
        task_logger = get_task_logger(task_id)
        
        # 确定字幕结果类型
        result_type = self._determine_subtitle_result_type(request)
        
        # 处理文字替换
        replace_words_map = self._parse_replace_words(request.replace_words)
        
        # 创建任务目录
        task_dir = create_task_directory(task_id)
        
        # 创建任务响应对象
        task_response = TaskResponse(
            task_id=task_id,
            status=TaskStatus.PROCESSING,
            progress=TaskProgress(
                current_step="初始化任务",
                total_steps=6,
                current_step_index=0,
                percent=0.0,
                message="任务已创建，开始处理..."
            ),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # 存储任务
        task_storage.store(task_id, task_response)
        
        # 构造任务步骤参数
        step_param = TaskStepParam(
            task_id=task_id,
            task_dir=str(task_dir),
            link=request.url,
            input_file_path=request.file_path,
            subtitle_result_type=result_type,
            enable_modal_filter=request.modal_filter,
            enable_tts=request.tts,
            tts_voice_code=request.tts_voice_code,
            replace_words_map=replace_words_map,
            origin_language=request.origin_language,
            target_language=request.target_language,
            user_ui_language="zh",  # 默认中文
            embed_subtitle_video_type=request.embed_subtitle_video_type,
            vertical_video_major_title=request.vertical_major_title,
            vertical_video_minor_title=request.vertical_minor_title,
            max_word_on_line=request.max_word_on_line
        )
        
        task_logger.info(f"任务创建成功: {task_id}")
        
        # 异步处理任务
        asyncio.create_task(self._process_task(step_param, task_response))
        
        return task_response
    
    async def get_task_status(self, task_id: str) -> TaskResponse:
        """
        获取任务状态
        对应Go版本的GetTaskStatus方法
        """
        task = task_storage.load(task_id)
        if not task:
            raise ValueError("任务不存在")
        
        if task.status == TaskStatus.FAILED:
            raise ValueError(f"任务失败，原因：{task.error_message}")
        
        return task
    
    async def _process_task(self, step_param: TaskStepParam, task_response: TaskResponse):
        """
        处理任务的主要流程
        对应Go版本的goroutine处理逻辑
        """
        task_logger = get_task_logger(step_param.task_id)
        
        try:
            # 步骤1: 链接到文件
            await self._update_progress(task_response, 1, "下载/处理输入文件", 16.7)
            await self.link_processor.process(step_param)
            
            # 步骤2: 音频转字幕
            await self._update_progress(task_response, 2, "语音识别生成字幕", 33.3)
            await self.audio_processor.process(step_param)
            
            # 步骤3: 翻译处理
            if step_param.target_language != "none":
                await self._update_progress(task_response, 3, "翻译字幕", 50.0)
                await self.translation_service.process(step_param)
            
            # 步骤4: 语音合成
            if step_param.enable_tts:
                await self._update_progress(task_response, 4, "生成语音", 66.7)
                await self.tts_service.process(step_param)
            
            # 步骤5: 视频合成
            await self._update_progress(task_response, 5, "合成视频", 83.3)
            await self.video_processor.process(step_param)
            
            # 步骤6: 上传结果
            await self._update_progress(task_response, 6, "上传结果文件", 100.0)
            await self._upload_results(step_param, task_response)
            
            # 任务完成
            task_response.status = TaskStatus.COMPLETED
            task_response.progress.message = "任务处理完成"
            task_response.updated_at = datetime.utcnow()
            
            task_logger.info(f"任务完成: {step_param.task_id}")
            
        except Exception as e:
            # 任务失败
            task_response.status = TaskStatus.FAILED
            task_response.error_message = str(e)
            task_response.updated_at = datetime.utcnow()
            
            task_logger.error(f"任务失败: {step_param.task_id}, 错误: {e}")
            task_logger.error(f"堆栈跟踪: {traceback.format_exc()}")
    
    def _determine_subtitle_result_type(self, request: TaskRequest) -> SubtitleResultType:
        """确定字幕结果类型"""
        if request.target_language == "none":
            return SubtitleResultType.ORIGIN_ONLY
        else:
            if request.bilingual:
                if request.translation_subtitle_pos == "top":
                    return SubtitleResultType.BILINGUAL_TRANSLATION_ON_TOP
                else:
                    return SubtitleResultType.BILINGUAL_TRANSLATION_ON_BOTTOM
            else:
                return SubtitleResultType.TARGET_ONLY
    
    def _parse_replace_words(self, replace_words: list) -> Dict[str, str]:
        """解析替换词汇"""
        replace_map = {}
        for replace_item in replace_words:
            if "|" in replace_item:
                before, after = replace_item.split("|", 1)
                replace_map[before.strip()] = after.strip()
        return replace_map
    
    async def _update_progress(self, task_response: TaskResponse, step: int, message: str, percent: float):
        """更新任务进度"""
        task_response.progress.current_step_index = step
        task_response.progress.current_step = message
        task_response.progress.percent = percent
        task_response.progress.message = message
        task_response.updated_at = datetime.utcnow()
        
        # 更新存储中的任务
        task_storage.store(task_response.task_id, task_response)
    
    async def _upload_results(self, step_param: TaskStepParam, task_response: TaskResponse):
        """上传结果文件"""
        # 这里实现文件上传逻辑
        # 可以上传到OSS、本地存储等
        task_dir = Path(step_param.task_dir)
        
        # 查找生成的字幕文件
        subtitle_files = list(task_dir.glob("*.srt")) + list(task_dir.glob("*.ass"))
        
        subtitle_infos = []
        for subtitle_file in subtitle_files:
            # 这里可以实现实际的文件上传逻辑
            # 现在只是返回本地路径
            subtitle_infos.append({
                "name": subtitle_file.name,
                "download_url": f"/download/{step_param.task_id}/{subtitle_file.name}",
                "format": subtitle_file.suffix[1:],
                "language": step_param.target_language
            })
        
        task_response.subtitle_infos = subtitle_infos
        
        # 如果有语音文件
        speech_files = list(task_dir.glob("*.mp3")) + list(task_dir.glob("*.wav"))
        if speech_files:
            task_response.speech_download_url = f"/download/{step_param.task_id}/{speech_files[0].name}"


# 全局字幕服务实例
subtitle_service = SubtitleService()
