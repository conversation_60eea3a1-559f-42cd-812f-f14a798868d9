"""
TTS服务 - 对应Go版本的srtFileToSpeech功能
"""

from pathlib import Path
from app.models.task import TaskStepParam
from app.utils.logger import get_task_logger


class TTSService:
    """TTS服务"""
    
    async def process(self, step_param: TaskStepParam):
        """
        处理字幕转语音
        """
        task_logger = get_task_logger(step_param.task_id)
        
        if not step_param.enable_tts:
            task_logger.info("跳过TTS步骤")
            return
        
        task_logger.info("TTS功能暂未实现")
        # 这里可以实现TTS功能
