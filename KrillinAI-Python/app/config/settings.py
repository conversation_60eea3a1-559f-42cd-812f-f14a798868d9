"""
配置管理模块
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseSettings, Field
from pathlib import Path
import toml


class OpenAIConfig(BaseSettings):
    """OpenAI配置"""
    api_key: str = ""
    base_url: str = "https://api.openai.com/v1"
    model: str = "gpt-3.5-turbo"
    timeout: int = 60


class DeepSeekConfig(BaseSettings):
    """DeepSeek配置"""
    api_key: str = ""
    base_url: str = "https://api.deepseek.com/v1"
    model: str = "deepseek-chat"
    timeout: int = 60


class AliyunConfig(BaseSettings):
    """阿里云配置"""
    access_key_id: str = ""
    access_key_secret: str = ""
    region: str = "cn-shanghai"
    
    # OSS配置
    oss_bucket: str = ""
    oss_endpoint: str = ""
    
    # ASR配置
    asr_app_key: str = ""
    
    # TTS配置
    tts_app_key: str = ""


class LocalModelConfig(BaseSettings):
    """本地模型配置"""
    # Whisper模型配置
    whisper_model_size: str = "large-v2"
    whisper_device: str = "auto"  # auto, cpu, cuda, mps
    
    # FasterWhisper配置
    faster_whisper_model_size: str = "large-v2"
    faster_whisper_device: str = "auto"
    faster_whisper_compute_type: str = "float16"
    
    # 模型缓存目录
    model_cache_dir: str = "./models"


class ServerConfig(BaseSettings):
    """服务器配置"""
    host: str = "127.0.0.1"
    port: int = 8888
    debug: bool = False
    workers: int = 1
    
    # 静态文件配置
    static_dir: str = "./static"
    template_dir: str = "./templates"
    
    # 上传配置
    upload_dir: str = "./uploads"
    max_upload_size: int = 500 * 1024 * 1024  # 500MB


class TaskConfig(BaseSettings):
    """任务配置"""
    # 任务存储目录
    task_dir: str = "./tasks"
    
    # 任务超时时间（秒）
    task_timeout: int = 3600
    
    # 最大并发任务数
    max_concurrent_tasks: int = 5
    
    # 任务清理时间（秒）
    task_cleanup_time: int = 86400  # 24小时


class RedisConfig(BaseSettings):
    """Redis配置"""
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None
    
    # Celery配置
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    url: str = "sqlite:///./krillin.db"
    echo: bool = False
    
    # 连接池配置
    pool_size: int = 5
    max_overflow: int = 10


class LogConfig(BaseSettings):
    """日志配置"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
    rotation: str = "1 day"
    retention: str = "7 days"
    log_dir: str = "./logs"


class Settings(BaseSettings):
    """主配置类"""
    
    # 应用信息
    app_name: str = "KrillinAI"
    app_version: str = "2.0.0"
    description: str = "AI视频翻译和配音工具"
    
    # 服务提供商配置
    transcription_provider: str = "openai"  # openai, faster_whisper, whisper_kit, aliyun
    llm_provider: str = "openai"  # openai, deepseek, qwen, aliyun
    tts_provider: str = "aliyun"  # aliyun, openai
    
    # 各服务配置
    openai: OpenAIConfig = OpenAIConfig()
    deepseek: DeepSeekConfig = DeepSeekConfig()
    aliyun: AliyunConfig = AliyunConfig()
    local_model: LocalModelConfig = LocalModelConfig()
    
    # 系统配置
    server: ServerConfig = ServerConfig()
    task: TaskConfig = TaskConfig()
    redis: RedisConfig = RedisConfig()
    database: DatabaseConfig = DatabaseConfig()
    log: LogConfig = LogConfig()
    
    # 支持的语言
    supported_languages: Dict[str, str] = {
        "zh": "中文",
        "en": "English",
        "ja": "日本語",
        "ko": "한국어",
        "de": "Deutsch",
        "fr": "Français",
        "es": "Español",
        "ru": "Русский",
        "pt": "Português",
        "it": "Italiano",
        "tr": "Türkçe",
        "ar": "العربية",
        "hi": "हिन्दी",
        "th": "ไทย",
        "vi": "Tiếng Việt",
        "ms": "Bahasa Melayu"
    }
    
    # 支持的视频格式
    supported_video_formats: List[str] = [
        ".mp4", ".avi", ".mov", ".mkv", ".flv", ".wmv", ".webm", ".m4v"
    ]
    
    # 支持的音频格式
    supported_audio_formats: List[str] = [
        ".mp3", ".wav", ".flac", ".aac", ".ogg", ".m4a", ".wma"
    ]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @classmethod
    def load_from_toml(cls, config_path: str = "app/config/config.toml") -> "Settings":
        """从TOML文件加载配置"""
        config_file = Path(config_path)
        if not config_file.exists():
            # 如果配置文件不存在，创建默认配置
            default_settings = cls()
            default_settings.save_to_toml(config_path)
            return default_settings
        
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config_data = toml.load(f)
            
            # 创建配置实例
            settings = cls()
            
            # 更新配置
            for section, values in config_data.items():
                if hasattr(settings, section):
                    section_config = getattr(settings, section)
                    if hasattr(section_config, '__dict__'):
                        for key, value in values.items():
                            if hasattr(section_config, key):
                                setattr(section_config, key, value)
                    else:
                        setattr(settings, section, values)
            
            return settings
            
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return cls()
    
    def save_to_toml(self, config_path: str = "app/config/config.toml") -> None:
        """保存配置到TOML文件"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        config_data = {}
        
        # 转换配置为字典
        for field_name, field_value in self.__dict__.items():
            if hasattr(field_value, '__dict__'):
                config_data[field_name] = field_value.__dict__
            else:
                config_data[field_name] = field_value
        
        try:
            with open(config_file, "w", encoding="utf-8") as f:
                toml.dump(config_data, f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")


# 全局配置实例
settings = Settings.load_from_toml()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
