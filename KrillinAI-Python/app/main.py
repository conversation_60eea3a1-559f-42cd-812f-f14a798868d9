"""
KrillinAI Python版本主应用
"""

import os
import sys
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config.settings import get_settings
from app.utils.logger import setup_logger
from app.api.routes import subtitle, task
from app.core.database import init_database


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    settings = get_settings()
    
    # 设置日志
    setup_logger(settings.log)
    
    # 创建必要的目录
    os.makedirs(settings.server.static_dir, exist_ok=True)
    os.makedirs(settings.server.upload_dir, exist_ok=True)
    os.makedirs(settings.task.task_dir, exist_ok=True)
    os.makedirs(settings.local_model.model_cache_dir, exist_ok=True)
    os.makedirs(settings.log.log_dir, exist_ok=True)
    
    # 初始化数据库
    await init_database()
    
    print(f"🚀 {settings.app_name} v{settings.app_version} 启动成功!")
    print(f"📡 服务地址: http://{settings.server.host}:{settings.server.port}")
    print(f"📚 API文档: http://{settings.server.host}:{settings.server.port}/docs")
    
    yield
    
    # 关闭时执行
    print("👋 KrillinAI 服务已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.app_name,
        description=settings.description,
        version=settings.app_version,
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 挂载静态文件
    if os.path.exists(settings.server.static_dir):
        app.mount("/static", StaticFiles(directory=settings.server.static_dir), name="static")
    
    # 注册路由
    app.include_router(subtitle.router, prefix="/api/v1", tags=["字幕"])
    app.include_router(task.router, prefix="/api/v1", tags=["任务"])
    
    # 模板引擎
    templates = Jinja2Templates(directory=settings.server.template_dir)
    
    @app.get("/", response_class=HTMLResponse)
    async def index(request: Request):
        """首页"""
        return templates.TemplateResponse(
            "index.html", 
            {
                "request": request,
                "app_name": settings.app_name,
                "app_version": settings.app_version,
                "supported_languages": settings.supported_languages
            }
        )
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "app_name": settings.app_name,
            "version": settings.app_version
        }
    
    @app.get("/config")
    async def get_config():
        """获取配置信息（脱敏）"""
        return {
            "transcription_provider": settings.transcription_provider,
            "llm_provider": settings.llm_provider,
            "tts_provider": settings.tts_provider,
            "supported_languages": settings.supported_languages,
            "supported_video_formats": settings.supported_video_formats,
            "supported_audio_formats": settings.supported_audio_formats,
            "max_upload_size": settings.server.max_upload_size
        }
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.server.host,
        port=settings.server.port,
        reload=settings.server.debug,
        workers=1 if settings.server.debug else settings.server.workers,
        log_level="info"
    )
