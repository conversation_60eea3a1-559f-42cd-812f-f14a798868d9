"""
字幕相关API路由
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse

from app.models.task import TaskRequest, TaskResponse
from app.core.subtitle_service import subtitle_service
from app.utils.logger import get_logger

router = APIRouter()
logger = get_logger()


@router.post("/subtitle/start", response_model=TaskResponse)
async def start_subtitle_task(
    # 视频链接或文件上传
    url: Optional[str] = Form(None),
    file: Optional[UploadFile] = File(None),
    
    # 语言设置
    origin_language: str = Form("auto"),
    target_language: str = Form("zh"),
    
    # 字幕设置
    bilingual: bool = Form(True),
    translation_subtitle_pos: str = Form("bottom"),
    
    # 处理选项
    modal_filter: bool = Form(True),
    tts: bool = Form(False),
    tts_voice_code: str = Form("longyu"),
    
    # 替换词汇
    replace_words: str = Form(""),
    
    # 视频设置
    embed_subtitle_video_type: str = Form("horizontal"),
    vertical_major_title: Optional[str] = Form(None),
    vertical_minor_title: Optional[str] = Form(None),
    max_word_on_line: int = Form(12)
):
    """
    启动字幕生成任务
    
    支持两种输入方式：
    1. 通过url参数提供视频链接（YouTube、Bilibili等）
    2. 通过file参数上传本地视频/音频文件
    """
    try:
        # 验证输入
        if not url and not file:
            raise HTTPException(status_code=400, detail="必须提供视频链接或上传文件")
        
        if url and file:
            raise HTTPException(status_code=400, detail="不能同时提供链接和文件")
        
        # 处理文件上传
        file_path = None
        if file:
            # 保存上传的文件
            import os
            from app.config.settings import get_settings
            
            settings = get_settings()
            upload_dir = settings.server.upload_dir
            os.makedirs(upload_dir, exist_ok=True)
            
            file_path = os.path.join(upload_dir, file.filename)
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            logger.info(f"文件上传成功: {file.filename}, 大小: {len(content)} bytes")
        
        # 解析替换词汇
        replace_words_list = []
        if replace_words:
            replace_words_list = [word.strip() for word in replace_words.split(",") if word.strip()]
        
        # 创建任务请求
        task_request = TaskRequest(
            url=url,
            file_path=file_path,
            origin_language=origin_language,
            target_language=target_language,
            bilingual=bilingual,
            translation_subtitle_pos=translation_subtitle_pos,
            modal_filter=modal_filter,
            tts=tts,
            tts_voice_code=tts_voice_code,
            replace_words=replace_words_list,
            embed_subtitle_video_type=embed_subtitle_video_type,
            vertical_major_title=vertical_major_title,
            vertical_minor_title=vertical_minor_title,
            max_word_on_line=max_word_on_line
        )
        
        # 启动任务
        task_response = await subtitle_service.start_subtitle_task(task_request)
        
        logger.info(f"字幕任务启动成功: {task_response.task_id}")
        
        return task_response
        
    except ValueError as e:
        logger.error(f"参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"启动字幕任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/subtitle/status/{task_id}", response_model=TaskResponse)
async def get_subtitle_task_status(task_id: str):
    """
    获取字幕任务状态
    """
    try:
        task_response = await subtitle_service.get_task_status(task_id)
        return task_response
        
    except ValueError as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.delete("/subtitle/task/{task_id}")
async def cancel_subtitle_task(task_id: str):
    """
    取消字幕任务
    """
    try:
        from app.models.task import task_storage, TaskStatus
        
        task = task_storage.load(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
            raise HTTPException(status_code=400, detail="任务已完成或失败，无法取消")
        
        # 更新任务状态为已取消
        task.status = TaskStatus.CANCELLED
        task.progress.message = "任务已取消"
        task_storage.store(task_id, task)
        
        logger.info(f"任务已取消: {task_id}")
        
        return {"message": "任务已取消", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/subtitle/tasks")
async def list_subtitle_tasks():
    """
    列出所有字幕任务
    """
    try:
        from app.models.task import task_storage
        
        tasks = task_storage.list_all()
        
        # 转换为列表格式
        task_list = []
        for task_id, task in tasks.items():
            task_list.append({
                "task_id": task_id,
                "status": task.status,
                "progress": task.progress.percent,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            })
        
        # 按创建时间倒序排列
        task_list.sort(key=lambda x: x["created_at"], reverse=True)
        
        return {
            "tasks": task_list,
            "total": len(task_list)
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/subtitle/languages")
async def get_supported_languages():
    """
    获取支持的语言列表
    """
    from app.config.settings import get_settings
    
    settings = get_settings()
    
    return {
        "supported_languages": settings.supported_languages,
        "default_origin": "auto",
        "default_target": "zh"
    }


@router.get("/subtitle/formats")
async def get_supported_formats():
    """
    获取支持的文件格式
    """
    from app.config.settings import get_settings
    
    settings = get_settings()
    
    return {
        "video_formats": settings.supported_video_formats,
        "audio_formats": settings.supported_audio_formats,
        "max_upload_size": settings.server.max_upload_size
    }
