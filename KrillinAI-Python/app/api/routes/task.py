"""
任务管理API路由
"""

import os
from pathlib import Path
from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import FileResponse, JSONResponse

from app.models.task import task_storage
from app.utils.logger import get_logger
from app.config.settings import get_settings

router = APIRouter()
logger = get_logger()


@router.get("/task/{task_id}")
async def get_task_detail(task_id: str):
    """
    获取任务详细信息
    """
    try:
        task = task_storage.load(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """
    删除任务及其相关文件
    """
    try:
        task = task_storage.load(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 删除任务文件夹
        settings = get_settings()
        task_dir = Path(settings.task.task_dir) / task_id
        
        if task_dir.exists():
            import shutil
            shutil.rmtree(task_dir)
            logger.info(f"删除任务文件夹: {task_dir}")
        
        # 从内存中删除任务
        task_storage.delete(task_id)
        
        logger.info(f"任务已删除: {task_id}")
        
        return {"message": "任务已删除", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/download/{task_id}/{filename}")
async def download_file(task_id: str, filename: str):
    """
    下载任务生成的文件
    """
    try:
        # 验证任务存在
        task = task_storage.load(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 构造文件路径
        settings = get_settings()
        file_path = Path(settings.task.task_dir) / task_id / "output" / filename
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 检查文件是否在任务目录内（安全检查）
        task_dir = Path(settings.task.task_dir) / task_id
        try:
            file_path.resolve().relative_to(task_dir.resolve())
        except ValueError:
            raise HTTPException(status_code=403, detail="访问被拒绝")
        
        # 确定媒体类型
        media_type = "application/octet-stream"
        if filename.endswith(('.srt', '.ass', '.vtt')):
            media_type = "text/plain"
        elif filename.endswith(('.mp4', '.avi', '.mov')):
            media_type = "video/mp4"
        elif filename.endswith(('.mp3', '.wav')):
            media_type = "audio/mpeg"
        
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type=media_type
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/task/{task_id}/files")
async def list_task_files(task_id: str):
    """
    列出任务生成的所有文件
    """
    try:
        # 验证任务存在
        task = task_storage.load(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 获取任务目录
        settings = get_settings()
        task_dir = Path(settings.task.task_dir) / task_id
        
        if not task_dir.exists():
            return {"files": []}
        
        files = []
        
        # 遍历任务目录下的所有文件
        for file_path in task_dir.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(task_dir)
                file_info = {
                    "name": file_path.name,
                    "path": str(relative_path),
                    "size": file_path.stat().st_size,
                    "created_at": file_path.stat().st_ctime,
                    "download_url": f"/api/v1/download/{task_id}/{file_path.name}"
                }
                
                # 添加文件类型信息
                if file_path.suffix in ['.srt', '.ass', '.vtt']:
                    file_info["type"] = "subtitle"
                elif file_path.suffix in ['.mp4', '.avi', '.mov', '.mkv']:
                    file_info["type"] = "video"
                elif file_path.suffix in ['.mp3', '.wav', '.flac']:
                    file_info["type"] = "audio"
                else:
                    file_info["type"] = "other"
                
                files.append(file_info)
        
        # 按文件类型和名称排序
        files.sort(key=lambda x: (x["type"], x["name"]))
        
        return {"files": files, "total": len(files)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/tasks/stats")
async def get_tasks_stats():
    """
    获取任务统计信息
    """
    try:
        from app.models.task import TaskStatus
        
        tasks = task_storage.list_all()
        
        stats = {
            "total": len(tasks),
            "pending": 0,
            "processing": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0
        }
        
        for task in tasks.values():
            if task.status == TaskStatus.PENDING:
                stats["pending"] += 1
            elif task.status == TaskStatus.PROCESSING:
                stats["processing"] += 1
            elif task.status == TaskStatus.COMPLETED:
                stats["completed"] += 1
            elif task.status == TaskStatus.FAILED:
                stats["failed"] += 1
            elif task.status == TaskStatus.CANCELLED:
                stats["cancelled"] += 1
        
        return stats
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/tasks/cleanup")
async def cleanup_completed_tasks():
    """
    清理已完成的任务
    """
    try:
        from app.models.task import TaskStatus
        from datetime import datetime, timedelta
        
        settings = get_settings()
        cleanup_time = timedelta(seconds=settings.task.task_cleanup_time)
        cutoff_time = datetime.utcnow() - cleanup_time
        
        tasks = task_storage.list_all()
        cleaned_count = 0
        
        for task_id, task in tasks.items():
            # 清理超过指定时间的已完成或失败任务
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and 
                task.updated_at < cutoff_time):
                
                # 删除任务文件夹
                task_dir = Path(settings.task.task_dir) / task_id
                if task_dir.exists():
                    import shutil
                    shutil.rmtree(task_dir)
                
                # 从内存中删除任务
                task_storage.delete(task_id)
                cleaned_count += 1
                
                logger.info(f"清理任务: {task_id}")
        
        return {
            "message": f"清理完成，共清理 {cleaned_count} 个任务",
            "cleaned_count": cleaned_count
        }
        
    except Exception as e:
        logger.error(f"清理任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/system/info")
async def get_system_info():
    """
    获取系统信息
    """
    try:
        import psutil
        import platform
        
        # 系统信息
        system_info = {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "architecture": platform.machine(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "disk_usage": psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
        }
        
        # 应用信息
        settings = get_settings()
        app_info = {
            "app_name": settings.app_name,
            "app_version": settings.app_version,
            "transcription_provider": settings.transcription_provider,
            "llm_provider": settings.llm_provider,
            "tts_provider": settings.tts_provider
        }
        
        return {
            "system": system_info,
            "application": app_info
        }
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        # 如果psutil不可用，返回基本信息
        settings = get_settings()
        return {
            "system": {"platform": "unknown"},
            "application": {
                "app_name": settings.app_name,
                "app_version": settings.app_version
            }
        }
