"""
任务数据模型
"""

from enum import Enum
from typing import Optional, Dict, List, Any
from datetime import datetime
from pydantic import BaseModel, Field
from sqlalchemy import Column, String, Integer, DateTime, Text, JSON, Float
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class TaskStatus(str, Enum):
    """任务状态枚举"""

    PENDING = "pending"  # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


class TaskType(str, Enum):
    """任务类型枚举"""

    VIDEO_SUBTITLE = "video_subtitle"  # 视频字幕生成
    AUDIO_SUBTITLE = "audio_subtitle"  # 音频字幕生成
    TRANSLATION = "translation"  # 翻译
    TTS = "tts"  # 语音合成


class SubtitleResultType(str, Enum):
    """字幕结果类型"""

    ORIGIN_ONLY = "origin_only"  # 仅原文
    TARGET_ONLY = "target_only"  # 仅译文
    BILINGUAL_TRANSLATION_ON_TOP = "bilingual_translation_on_top"  # 双语-译文在上
    BILINGUAL_TRANSLATION_ON_BOTTOM = "bilingual_translation_on_bottom"  # 双语-译文在下


class VideoInfo(BaseModel):
    """视频信息"""

    title: Optional[str] = None
    description: Optional[str] = None
    duration: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    fps: Optional[float] = None
    format: Optional[str] = None
    size: Optional[int] = None


class SubtitleInfo(BaseModel):
    """字幕信息"""

    name: str
    download_url: str
    format: str = "srt"
    language: str = "zh"


class TaskProgress(BaseModel):
    """任务进度"""

    current_step: str = ""
    total_steps: int = 0
    current_step_index: int = 0
    percent: float = 0.0
    message: str = ""


class TaskRequest(BaseModel):
    """任务请求模型"""

    url: Optional[str] = None  # 视频链接
    file_path: Optional[str] = None  # 本地文件路径
    origin_language: str = "auto"  # 原始语言
    target_language: str = "zh"  # 目标语言
    bilingual: bool = True  # 是否双语
    translation_subtitle_pos: str = "bottom"  # 翻译字幕位置
    modal_filter: bool = True  # 模态过滤
    tts: bool = False  # 是否生成语音
    tts_voice_code: str = "longyu"  # TTS音色代码
    voice_clone_src_file_url: Optional[str] = None  # 声音克隆源文件
    replace_words: List[str] = Field(default_factory=list)  # 替换词汇
    embed_subtitle_video_type: str = "horizontal"  # 嵌入字幕视频类型
    vertical_major_title: Optional[str] = None  # 竖屏主标题
    vertical_minor_title: Optional[str] = None  # 竖屏副标题
    max_word_on_line: int = 12  # 每行最大字数


class TaskResponse(BaseModel):
    """任务响应模型"""

    task_id: str
    status: TaskStatus
    progress: TaskProgress
    video_info: Optional[VideoInfo] = None
    subtitle_infos: List[SubtitleInfo] = Field(default_factory=list)
    speech_download_url: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class TaskDB(Base):
    """任务数据库模型"""

    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(50), unique=True, index=True, nullable=False)
    task_type = Column(String(50), nullable=False)
    status = Column(String(20), nullable=False, default=TaskStatus.PENDING)

    # 请求参数
    request_data = Column(JSON, nullable=True)

    # 进度信息
    progress_percent = Column(Float, default=0.0)
    current_step = Column(String(100), nullable=True)

    # 结果信息
    video_info = Column(JSON, nullable=True)
    subtitle_infos = Column(JSON, nullable=True)
    speech_download_url = Column(String(500), nullable=True)

    # 错误信息
    error_message = Column(Text, nullable=True)

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 文件路径
    task_dir = Column(String(500), nullable=True)
    input_file_path = Column(String(500), nullable=True)
    output_file_path = Column(String(500), nullable=True)


class TaskStepParam(BaseModel):
    """任务步骤参数"""

    task_id: str
    task_dir: str
    link: Optional[str] = None
    input_file_path: Optional[str] = None
    subtitle_result_type: SubtitleResultType = (
        SubtitleResultType.BILINGUAL_TRANSLATION_ON_BOTTOM
    )
    enable_modal_filter: bool = True
    enable_tts: bool = False
    tts_voice_code: str = "longyu"
    voice_clone_audio_url: Optional[str] = None
    replace_words_map: Dict[str, str] = Field(default_factory=dict)
    origin_language: str = "auto"
    target_language: str = "zh"
    user_ui_language: str = "zh"
    embed_subtitle_video_type: str = "horizontal"
    vertical_video_major_title: Optional[str] = None
    vertical_video_minor_title: Optional[str] = None
    max_word_on_line: int = 12


# 内存中的任务存储（类似Go版本的storage.SubtitleTasks）
class TaskStorage:
    """任务内存存储"""

    def __init__(self):
        self._tasks: Dict[str, TaskResponse] = {}

    def store(self, task_id: str, task: TaskResponse) -> None:
        """存储任务"""
        self._tasks[task_id] = task

    def load(self, task_id: str) -> Optional[TaskResponse]:
        """加载任务"""
        return self._tasks.get(task_id)

    def delete(self, task_id: str) -> bool:
        """删除任务"""
        if task_id in self._tasks:
            del self._tasks[task_id]
            return True
        return False

    def list_all(self) -> Dict[str, TaskResponse]:
        """列出所有任务"""
        return self._tasks.copy()


# 全局任务存储实例
task_storage = TaskStorage()
