"""
日志工具模块
"""

import sys
from pathlib import Path
from loguru import logger
from app.config.settings import LogConfig


def setup_logger(log_config: LogConfig) -> None:
    """设置日志配置"""
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 创建日志目录
    log_dir = Path(log_config.log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 控制台日志
    logger.add(
        sys.stdout,
        format=log_config.format,
        level=log_config.level,
        colorize=True
    )
    
    # 文件日志 - 普通日志
    logger.add(
        log_dir / "krillin.log",
        format=log_config.format,
        level=log_config.level,
        rotation=log_config.rotation,
        retention=log_config.retention,
        compression="zip",
        encoding="utf-8"
    )
    
    # 文件日志 - 错误日志
    logger.add(
        log_dir / "error.log",
        format=log_config.format,
        level="ERROR",
        rotation=log_config.rotation,
        retention=log_config.retention,
        compression="zip",
        encoding="utf-8"
    )
    
    # 文件日志 - 任务日志
    logger.add(
        log_dir / "task.log",
        format=log_config.format,
        level="INFO",
        rotation=log_config.rotation,
        retention=log_config.retention,
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "task" in record["extra"]
    )


def get_logger():
    """获取日志器实例"""
    return logger


def get_task_logger(task_id: str):
    """获取任务专用日志器"""
    return logger.bind(task=task_id)
