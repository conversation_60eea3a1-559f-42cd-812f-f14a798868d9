"""
文件工具模块
"""

import os
import random
import string
import shutil
from pathlib import Path
from typing import Optional

from app.config.settings import get_settings


def generate_random_string(length: int = 8) -> str:
    """
    生成随机字符串
    对应Go版本的GenerateRandStringWithUpperLowerNum
    """
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


def create_task_directory(task_id: str) -> Path:
    """
    创建任务目录
    对应Go版本的MkdirAll逻辑
    """
    settings = get_settings()
    task_dir = Path(settings.task.task_dir) / task_id
    
    # 创建任务目录和输出目录
    task_dir.mkdir(parents=True, exist_ok=True)
    (task_dir / "output").mkdir(exist_ok=True)
    (task_dir / "temp").mkdir(exist_ok=True)
    
    return task_dir


def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return Path(filename).suffix.lower()


def is_video_file(filename: str) -> bool:
    """检查是否为视频文件"""
    settings = get_settings()
    ext = get_file_extension(filename)
    return ext in settings.supported_video_formats


def is_audio_file(filename: str) -> bool:
    """检查是否为音频文件"""
    settings = get_settings()
    ext = get_file_extension(filename)
    return ext in settings.supported_audio_formats


def get_file_size(file_path: str) -> int:
    """获取文件大小（字节）"""
    return os.path.getsize(file_path)


def ensure_directory(directory: str) -> None:
    """确保目录存在"""
    Path(directory).mkdir(parents=True, exist_ok=True)


def copy_file(src: str, dst: str) -> None:
    """复制文件"""
    ensure_directory(os.path.dirname(dst))
    shutil.copy2(src, dst)


def move_file(src: str, dst: str) -> None:
    """移动文件"""
    ensure_directory(os.path.dirname(dst))
    shutil.move(src, dst)


def delete_file(file_path: str) -> bool:
    """删除文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception:
        return False


def delete_directory(directory: str) -> bool:
    """删除目录"""
    try:
        if os.path.exists(directory):
            shutil.rmtree(directory)
            return True
        return False
    except Exception:
        return False


def get_safe_filename(filename: str) -> str:
    """获取安全的文件名（移除特殊字符）"""
    # 移除或替换不安全的字符
    safe_chars = "-_.() abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    safe_filename = ''.join(c for c in filename if c in safe_chars)
    
    # 限制长度
    if len(safe_filename) > 100:
        name, ext = os.path.splitext(safe_filename)
        safe_filename = name[:100-len(ext)] + ext
    
    return safe_filename


def get_unique_filename(directory: str, filename: str) -> str:
    """获取唯一的文件名（如果文件已存在，添加数字后缀）"""
    base_path = Path(directory) / filename
    
    if not base_path.exists():
        return filename
    
    name, ext = os.path.splitext(filename)
    counter = 1
    
    while True:
        new_filename = f"{name}_{counter}{ext}"
        new_path = Path(directory) / new_filename
        
        if not new_path.exists():
            return new_filename
        
        counter += 1


def read_text_file(file_path: str, encoding: str = "utf-8") -> str:
    """读取文本文件"""
    with open(file_path, "r", encoding=encoding) as f:
        return f.read()


def write_text_file(file_path: str, content: str, encoding: str = "utf-8") -> None:
    """写入文本文件"""
    ensure_directory(os.path.dirname(file_path))
    with open(file_path, "w", encoding=encoding) as f:
        f.write(content)


def append_text_file(file_path: str, content: str, encoding: str = "utf-8") -> None:
    """追加到文本文件"""
    ensure_directory(os.path.dirname(file_path))
    with open(file_path, "a", encoding=encoding) as f:
        f.write(content)


def list_files(directory: str, pattern: str = "*", recursive: bool = False) -> list:
    """列出目录中的文件"""
    path = Path(directory)
    
    if not path.exists():
        return []
    
    if recursive:
        return [str(f) for f in path.rglob(pattern) if f.is_file()]
    else:
        return [str(f) for f in path.glob(pattern) if f.is_file()]


def get_file_info(file_path: str) -> dict:
    """获取文件信息"""
    path = Path(file_path)
    
    if not path.exists():
        return {}
    
    stat = path.stat()
    
    return {
        "name": path.name,
        "size": stat.st_size,
        "created_at": stat.st_ctime,
        "modified_at": stat.st_mtime,
        "is_file": path.is_file(),
        "is_directory": path.is_dir(),
        "extension": path.suffix.lower()
    }


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def cleanup_temp_files(task_id: str) -> None:
    """清理临时文件"""
    settings = get_settings()
    temp_dir = Path(settings.task.task_dir) / task_id / "temp"
    
    if temp_dir.exists():
        try:
            shutil.rmtree(temp_dir)
        except Exception:
            pass  # 忽略清理错误
