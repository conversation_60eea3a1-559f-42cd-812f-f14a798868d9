"""
视频工具模块
对应Go版本的util包中的视频相关功能
"""

import re
from urllib.parse import urlparse, parse_qs
from typing import Optional


def get_youtube_id(url: str) -> Optional[str]:
    """
    从YouTube URL中提取视频ID
    对应Go版本的GetYouTubeID函数
    """
    if not url:
        return None
    
    # YouTube URL的各种格式
    patterns = [
        r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})',
        r'youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})',
        r'youtu\.be\/([a-zA-Z0-9_-]{11})',
        r'youtube\.com\/embed\/([a-zA-Z0-9_-]{11})',
        r'youtube\.com\/v\/([a-zA-Z0-9_-]{11})',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None


def get_bilibili_video_id(url: str) -> Optional[str]:
    """
    从Bilibili URL中提取视频ID
    对应Go版本的GetBilibiliVideoId函数
    """
    if not url:
        return None
    
    # Bilibili URL的各种格式
    patterns = [
        r'bilibili\.com\/video\/(BV[a-zA-Z0-9]+)',
        r'bilibili\.com\/video\/(av\d+)',
        r'b23\.tv\/([a-zA-Z0-9]+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None


def is_youtube_url(url: str) -> bool:
    """检查是否为YouTube URL"""
    if not url:
        return False
    
    youtube_domains = [
        'youtube.com',
        'www.youtube.com',
        'youtu.be',
        'm.youtube.com'
    ]
    
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower() in youtube_domains
    except Exception:
        return False


def is_bilibili_url(url: str) -> bool:
    """检查是否为Bilibili URL"""
    if not url:
        return False
    
    bilibili_domains = [
        'bilibili.com',
        'www.bilibili.com',
        'b23.tv',
        'm.bilibili.com'
    ]
    
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower() in bilibili_domains
    except Exception:
        return False


def is_supported_video_url(url: str) -> bool:
    """检查是否为支持的视频URL"""
    return is_youtube_url(url) or is_bilibili_url(url)


def get_video_platform(url: str) -> Optional[str]:
    """获取视频平台名称"""
    if is_youtube_url(url):
        return "youtube"
    elif is_bilibili_url(url):
        return "bilibili"
    else:
        return None


def normalize_youtube_url(url: str) -> Optional[str]:
    """标准化YouTube URL"""
    video_id = get_youtube_id(url)
    if video_id:
        return f"https://www.youtube.com/watch?v={video_id}"
    return None


def normalize_bilibili_url(url: str) -> Optional[str]:
    """标准化Bilibili URL"""
    video_id = get_bilibili_video_id(url)
    if video_id:
        return f"https://www.bilibili.com/video/{video_id}"
    return None


def extract_video_info_from_url(url: str) -> dict:
    """从URL中提取视频信息"""
    info = {
        "url": url,
        "platform": None,
        "video_id": None,
        "normalized_url": None
    }
    
    if is_youtube_url(url):
        info["platform"] = "youtube"
        info["video_id"] = get_youtube_id(url)
        info["normalized_url"] = normalize_youtube_url(url)
    elif is_bilibili_url(url):
        info["platform"] = "bilibili"
        info["video_id"] = get_bilibili_video_id(url)
        info["normalized_url"] = normalize_bilibili_url(url)
    
    return info


def get_video_quality_options() -> dict:
    """获取视频质量选项"""
    return {
        "best": "最佳质量",
        "worst": "最低质量",
        "720p": "720p",
        "480p": "480p",
        "360p": "360p",
        "audio_only": "仅音频"
    }


def get_video_format_options() -> dict:
    """获取视频格式选项"""
    return {
        "mp4": "MP4",
        "webm": "WebM",
        "mkv": "MKV",
        "avi": "AVI"
    }


def validate_video_url(url: str) -> tuple[bool, str]:
    """
    验证视频URL
    返回 (是否有效, 错误信息)
    """
    if not url:
        return False, "URL不能为空"
    
    if not url.startswith(('http://', 'https://')):
        return False, "URL必须以http://或https://开头"
    
    if not is_supported_video_url(url):
        return False, "不支持的视频平台，目前仅支持YouTube和Bilibili"
    
    # 检查YouTube URL
    if is_youtube_url(url):
        video_id = get_youtube_id(url)
        if not video_id:
            return False, "无效的YouTube URL"
        if len(video_id) != 11:
            return False, "YouTube视频ID格式错误"
    
    # 检查Bilibili URL
    elif is_bilibili_url(url):
        video_id = get_bilibili_video_id(url)
        if not video_id:
            return False, "无效的Bilibili URL"
    
    return True, ""


def get_download_command(url: str, output_path: str, quality: str = "best") -> list:
    """
    获取yt-dlp下载命令
    """
    cmd = [
        "yt-dlp",
        "--extract-flat", "false",
        "--write-info-json",
        "--write-thumbnail",
        "--output", output_path,
    ]
    
    # 设置质量
    if quality == "audio_only":
        cmd.extend([
            "--format", "bestaudio",
            "--extract-audio",
            "--audio-format", "mp3"
        ])
    else:
        if quality == "best":
            cmd.extend(["--format", "best"])
        elif quality == "worst":
            cmd.extend(["--format", "worst"])
        else:
            cmd.extend(["--format", f"best[height<={quality[:-1]}]"])
    
    cmd.append(url)
    return cmd


def parse_duration(duration_str: str) -> Optional[float]:
    """
    解析时长字符串为秒数
    支持格式: "1:23:45", "23:45", "45"
    """
    if not duration_str:
        return None
    
    try:
        parts = duration_str.split(':')
        if len(parts) == 1:
            return float(parts[0])
        elif len(parts) == 2:
            return float(parts[0]) * 60 + float(parts[1])
        elif len(parts) == 3:
            return float(parts[0]) * 3600 + float(parts[1]) * 60 + float(parts[2])
        else:
            return None
    except (ValueError, IndexError):
        return None


def format_duration(seconds: float) -> str:
    """
    格式化时长为字符串
    """
    if seconds < 0:
        return "00:00"
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes:02d}:{secs:02d}"
