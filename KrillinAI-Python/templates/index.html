<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app_name }} - AI视频翻译和配音工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .main-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e1e5e9;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s;
        }
        
        .task-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .file-list {
            list-style: none;
            margin-top: 15px;
        }
        
        .file-list li {
            background: white;
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .download-btn {
            background: #28a745;
            color: white;
            text-decoration: none;
            padding: 5px 15px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .download-btn:hover {
            background: #218838;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 {{ app_name }}</h1>
            <p>{{ description }} v{{ app_version }}</p>
        </div>
        
        <div class="main-card">
            <h2>🚀 开始新任务</h2>
            <form id="subtitleForm">
                <div class="form-group">
                    <label>输入方式</label>
                    <div style="display: flex; gap: 20px; margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="inputType" value="url" checked>
                            视频链接
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="inputType" value="file">
                            本地文件
                        </label>
                    </div>
                </div>
                
                <div class="form-group" id="urlGroup">
                    <label for="url">视频链接 (YouTube/Bilibili)</label>
                    <input type="url" id="url" name="url" placeholder="https://www.youtube.com/watch?v=...">
                </div>
                
                <div class="form-group" id="fileGroup" style="display: none;">
                    <label for="file">选择文件</label>
                    <input type="file" id="file" name="file" accept="video/*,audio/*">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="originLanguage">原始语言</label>
                        <select id="originLanguage" name="originLanguage">
                            <option value="auto">自动检测</option>
                            {% for code, name in supported_languages.items() %}
                            <option value="{{ code }}">{{ name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="targetLanguage">目标语言</label>
                        <select id="targetLanguage" name="targetLanguage">
                            <option value="zh">中文</option>
                            {% for code, name in supported_languages.items() %}
                            <option value="{{ code }}">{{ name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="bilingual" name="bilingual" checked>
                        <label for="bilingual">生成双语字幕</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="tts" name="tts">
                        <label for="tts">生成配音</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="replaceWords">替换词汇 (可选)</label>
                    <textarea id="replaceWords" name="replaceWords" rows="3" 
                              placeholder="格式: 原词|替换词，每行一个，例如：AI|人工智能"></textarea>
                </div>
                
                <button type="submit" class="btn" id="submitBtn">
                    🎯 开始处理
                </button>
            </form>
        </div>
        
        <div class="progress-card" id="progressCard">
            <h2>📊 处理进度</h2>
            <div id="taskId" style="font-family: monospace; color: #666; margin-bottom: 10px;"></div>
            <div id="currentStep" style="font-weight: 600; margin-bottom: 10px;"></div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressPercent" style="text-align: center; font-weight: 600;"></div>
            
            <div class="task-info" id="taskInfo" style="display: none;">
                <h3>✅ 任务完成</h3>
                <p>您的字幕文件已生成完成，可以下载使用。</p>
                <ul class="file-list" id="fileList"></ul>
            </div>
            
            <div id="errorInfo" class="error" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 切换输入方式
        document.querySelectorAll('input[name="inputType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const urlGroup = document.getElementById('urlGroup');
                const fileGroup = document.getElementById('fileGroup');
                
                if (this.value === 'url') {
                    urlGroup.style.display = 'block';
                    fileGroup.style.display = 'none';
                } else {
                    urlGroup.style.display = 'none';
                    fileGroup.style.display = 'block';
                }
            });
        });

        // 表单提交
        document.getElementById('subtitleForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = document.getElementById('submitBtn');
            const progressCard = document.getElementById('progressCard');
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ 处理中...';
            
            try {
                const response = await fetch('/api/v1/subtitle/start', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // 显示进度卡片
                    progressCard.style.display = 'block';
                    document.getElementById('taskId').textContent = `任务ID: ${result.task_id}`;
                    
                    // 开始轮询任务状态
                    pollTaskStatus(result.task_id);
                } else {
                    throw new Error(result.detail || '启动任务失败');
                }
                
            } catch (error) {
                alert('错误: ' + error.message);
                submitBtn.disabled = false;
                submitBtn.textContent = '🎯 开始处理';
            }
        });

        // 轮询任务状态
        async function pollTaskStatus(taskId) {
            try {
                const response = await fetch(`/api/v1/subtitle/status/${taskId}`);
                const task = await response.json();
                
                if (response.ok) {
                    updateProgress(task);
                    
                    if (task.status === 'completed') {
                        showTaskComplete(task);
                    } else if (task.status === 'failed') {
                        showTaskError(task.error_message);
                    } else {
                        // 继续轮询
                        setTimeout(() => pollTaskStatus(taskId), 2000);
                    }
                } else {
                    throw new Error(task.detail || '获取任务状态失败');
                }
                
            } catch (error) {
                showTaskError(error.message);
            }
        }

        // 更新进度
        function updateProgress(task) {
            document.getElementById('currentStep').textContent = task.progress.current_step;
            document.getElementById('progressFill').style.width = task.progress.percent + '%';
            document.getElementById('progressPercent').textContent = Math.round(task.progress.percent) + '%';
        }

        // 显示任务完成
        function showTaskComplete(task) {
            document.getElementById('taskInfo').style.display = 'block';
            
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            if (task.subtitle_infos && task.subtitle_infos.length > 0) {
                task.subtitle_infos.forEach(file => {
                    const li = document.createElement('li');
                    li.innerHTML = `
                        <span>${file.name}</span>
                        <a href="${file.download_url}" class="download-btn" download>下载</a>
                    `;
                    fileList.appendChild(li);
                });
            }
            
            if (task.speech_download_url) {
                const li = document.createElement('li');
                li.innerHTML = `
                    <span>配音文件</span>
                    <a href="${task.speech_download_url}" class="download-btn" download>下载</a>
                `;
                fileList.appendChild(li);
            }
            
            // 重置表单
            resetForm();
        }

        // 显示任务错误
        function showTaskError(errorMessage) {
            const errorInfo = document.getElementById('errorInfo');
            errorInfo.textContent = '任务失败: ' + errorMessage;
            errorInfo.style.display = 'block';
            
            resetForm();
        }

        // 重置表单
        function resetForm() {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = false;
            submitBtn.textContent = '🎯 开始处理';
        }
    </script>
</body>
</html>
