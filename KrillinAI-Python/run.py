#!/usr/bin/env python3
"""
KrillinAI Python版本启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        sys.exit(1)
    print(f"✅ Python版本检查通过: {sys.version}")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import fastapi
        import uvicorn
        print("✅ 核心依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("📦 正在安装依赖...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败，请手动运行: pip install -r requirements.txt")
            sys.exit(1)

def check_external_tools():
    """检查外部工具"""
    print("🔧 检查外部工具...")
    
    tools = {
        "ffmpeg": "FFmpeg (用于音视频处理)",
        "whisper": "OpenAI Whisper (用于语音识别)",
        "yt-dlp": "yt-dlp (用于视频下载)"
    }
    
    missing_tools = []
    
    for tool, description in tools.items():
        try:
            subprocess.run([tool, "--version"], 
                         capture_output=True, check=True)
            print(f"✅ {tool}: 已安装")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"⚠️  {tool}: 未安装 - {description}")
            missing_tools.append(tool)
    
    if missing_tools:
        print("\n📋 安装建议:")
        for tool in missing_tools:
            if tool == "ffmpeg":
                print("   FFmpeg: https://ffmpeg.org/download.html")
                print("   macOS: brew install ffmpeg")
                print("   Ubuntu: sudo apt install ffmpeg")
            elif tool == "whisper":
                print("   Whisper: pip install openai-whisper")
            elif tool == "yt-dlp":
                print("   yt-dlp: pip install yt-dlp")
        
        print("\n⚠️  部分功能可能无法正常使用")

def create_directories():
    """创建必要的目录"""
    print("📁 创建目录...")
    
    directories = [
        "static",
        "uploads", 
        "tasks",
        "models",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}/")

def create_config():
    """创建配置文件"""
    config_path = Path("app/config/config.toml")
    
    if not config_path.exists():
        print("⚙️  创建默认配置文件...")
        
        config_content = """
# KrillinAI Python版本配置文件

app_name = "KrillinAI"
app_version = "2.0.0"
description = "AI视频翻译和配音工具"

# 服务提供商配置
transcription_provider = "openai"  # openai, faster_whisper, whisper_kit, aliyun
llm_provider = "openai"  # openai, deepseek, qwen, aliyun
tts_provider = "aliyun"  # aliyun, openai

[openai]
api_key = ""  # 请填入你的OpenAI API密钥
base_url = "https://api.openai.com/v1"
model = "gpt-3.5-turbo"
timeout = 60

[deepseek]
api_key = ""
base_url = "https://api.deepseek.com/v1"
model = "deepseek-chat"
timeout = 60

[aliyun]
access_key_id = ""
access_key_secret = ""
region = "cn-shanghai"
oss_bucket = ""
oss_endpoint = ""
asr_app_key = ""
tts_app_key = ""

[local_model]
whisper_model_size = "large-v2"
whisper_device = "auto"
faster_whisper_model_size = "large-v2"
faster_whisper_device = "auto"
faster_whisper_compute_type = "float16"
model_cache_dir = "./models"

[server]
host = "127.0.0.1"
port = 8888
debug = false
workers = 1
static_dir = "./static"
template_dir = "./templates"
upload_dir = "./uploads"
max_upload_size = 524288000  # 500MB

[task]
task_dir = "./tasks"
task_timeout = 3600
max_concurrent_tasks = 5
task_cleanup_time = 86400

[redis]
host = "localhost"
port = 6379
db = 0
password = ""
celery_broker_url = "redis://localhost:6379/0"
celery_result_backend = "redis://localhost:6379/0"

[database]
url = "sqlite:///./krillin.db"
echo = false
pool_size = 5
max_overflow = 10

[log]
level = "INFO"
format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
rotation = "1 day"
retention = "7 days"
log_dir = "./logs"
"""
        
        config_path.parent.mkdir(exist_ok=True)
        with open(config_path, "w", encoding="utf-8") as f:
            f.write(config_content.strip())
        
        print(f"✅ 配置文件已创建: {config_path}")
        print("⚠️  请编辑配置文件，填入必要的API密钥")
    else:
        print(f"✅ 配置文件已存在: {config_path}")

def main():
    """主函数"""
    print("🚀 KrillinAI Python版本启动检查")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 检查依赖
    check_dependencies()
    
    # 检查外部工具
    check_external_tools()
    
    # 创建目录
    create_directories()
    
    # 创建配置
    create_config()
    
    print("\n" + "=" * 50)
    print("✅ 环境检查完成!")
    print("\n📋 下一步:")
    print("1. 编辑 app/config/config.toml 配置文件")
    print("2. 填入必要的API密钥")
    print("3. 运行服务: python -m app.main")
    print("4. 访问: http://127.0.0.1:8888")
    print("\n💡 快速启动:")
    print("   python -m app.main")

if __name__ == "__main__":
    main()
