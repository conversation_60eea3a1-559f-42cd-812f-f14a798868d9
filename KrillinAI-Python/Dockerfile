FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 安装外部工具
RUN pip install openai-whisper yt-dlp

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p uploads tasks logs models static

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8888

# 启动命令
CMD ["python", "-m", "app.main"]
