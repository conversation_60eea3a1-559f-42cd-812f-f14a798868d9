version: '3.8'

services:
  krillin-ai:
    build: .
    ports:
      - "8888:8888"
    volumes:
      - ./uploads:/app/uploads
      - ./tasks:/app/tasks
      - ./logs:/app/logs
      - ./app/config/config.toml:/app/app/config/config.toml
    environment:
      - PYTHONPATH=/app
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
