#!/usr/bin/env python3
"""
高质量双语视频字幕生成工具 (MPS加速版)

功能特点:
- 使用Whisper large模型进行高质量语音转录
- 支持MPS (Metal Performance Shaders) GPU加速 (Apple Silicon Mac)
- 自动检测并使用最佳设备 (MPS > CUDA > CPU)
- 自动生成英文字幕和中文翻译
- 支持关键词高亮（不同优先级，不同颜色）
- 生成ASS格式字幕，支持丰富的样式
- 自动合成带字幕的最终视频
- 支持分阶段运行

使用方法:
1. 将MP4视频文件放入 'videos' 目录
2. 运行脚本: python process_videos_mps.py
3. 处理完成的视频将保存在 'output' 目录

分阶段运行:
- python process_videos_mps.py --stage 1  # 仅转录
- python process_videos_mps.py --stage 2  # 合并字幕
- python process_videos_mps.py --stage 3  # 生成ASS
- python process_videos_mps.py --stage 4  # 合成视频

依赖要求:
- whisper (pip install openai-whisper)
- torch (支持MPS的版本)
- ffmpeg (系统安装)

作者: AI Assistant
版本: 2.1 (MPS加速版)
"""

import os
import re
import subprocess
import argparse
import platform
from glob import glob

# 导入必要的库
try:
    import torch

    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️  PyTorch未安装，将使用CPU模式")

try:
    import whisper

    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    print("❌ Whisper未安装，请运行: pip install openai-whisper")

VIDEO_DIR = "videos"
SUBTITLE_DIR = "subtitles"
OUTPUT_DIR = "output"

# FFmpeg路径配置 - 优先使用Homebrew版本
FFMPEG_PATH = "/opt/homebrew/bin/ffmpeg"
if not os.path.exists(FFMPEG_PATH):
    FFMPEG_PATH = "ffmpeg"  # 回退到系统PATH中的ffmpeg

# 关键词配置 - 可以根据需要添加更多关键词
KEYWORDS = [
    "AI",
    "NVIDIA",
    "future",
    "students",
    "learning",
    "innovation",
    "technology",
    "artificial intelligence",
    "machine learning",
    "deep learning",
    "GPU",
    "computing",
    "data",
    "algorithm",
    "neural network",
    "robotics",
    "automation",
    "digital transformation",
]

# 关键词高亮样式配置
KEYWORD_STYLES = {
    "high_priority": r"{\\fs28\\c&HFF0000&\\b1}",  # 红色加粗
    "medium_priority": r"{\\fs26\\c&H00FF00&\\b1}",  # 绿色加粗
    "normal": r"{\\fs24\\c&HFFFFFF&\\b0}",  # 白色正常
}


def detect_device():
    """检测并返回最佳的计算设备"""
    if not TORCH_AVAILABLE:
        print("🔧 使用CPU模式 (PyTorch未安装)")
        return "cpu"

    # 检测MPS支持 (Apple Silicon Mac)
    if torch.backends.mps.is_available():
        print("🚀 检测到MPS支持，将使用Apple Silicon GPU加速")
        return "mps"

    # 检测CUDA支持 (NVIDIA GPU)
    elif torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"🚀 检测到CUDA支持，将使用GPU加速: {gpu_name}")
        return "cuda"

    # 回退到CPU
    else:
        print("🔧 使用CPU模式")
        return "cpu"


def get_system_info():
    """获取系统信息"""
    system = platform.system()
    machine = platform.machine()

    if system == "Darwin" and machine == "arm64":
        return "Apple Silicon Mac"
    elif system == "Darwin" and machine == "x86_64":
        return "Intel Mac"
    elif system == "Linux":
        return "Linux"
    elif system == "Windows":
        return "Windows"
    else:
        return f"{system} {machine}"


def load_whisper_model(model_size="large", device=None):
    """加载Whisper模型到指定设备"""
    if not WHISPER_AVAILABLE:
        raise ImportError("Whisper未安装，请运行: pip install openai-whisper")

    if device is None:
        device = detect_device()

    print(f"📦 加载Whisper {model_size} 模型到 {device.upper()} 设备...")

    # 设备优先级列表：首选设备 -> 回退设备
    device_fallback = {"mps": ["mps", "cpu"], "cuda": ["cuda", "cpu"], "cpu": ["cpu"]}

    devices_to_try = device_fallback.get(device, ["cpu"])

    for try_device in devices_to_try:
        try:
            print(f"🔄 尝试加载到 {try_device.upper()}...")

            # 加载模型
            model = whisper.load_model(model_size, device=try_device)

            # 验证模型是否正常工作
            print(f"🧪 测试模型功能...")
            test_result = test_whisper_model(model, try_device)

            if test_result:
                print(f"✅ 模型已成功加载并验证: {try_device.upper()}")
                return model, try_device
            else:
                print(f"⚠️  模型加载成功但功能测试失败: {try_device}")
                continue

        except Exception as e:
            print(f"⚠️  {try_device.upper()} 加载失败: {str(e)[:100]}...")
            if try_device == devices_to_try[-1]:  # 最后一个设备也失败了
                print(f"❌ 所有设备都失败，将使用命令行Whisper")
                raise
            continue

    # 如果所有设备都失败，抛出异常
    raise RuntimeError("无法在任何设备上加载Whisper模型")


def test_whisper_model(model, device):
    """测试Whisper模型是否正常工作"""
    try:
        # 创建一个简单的测试音频（静音）
        try:
            import numpy as np

            # 生成1秒的静音音频 (16kHz采样率)
            test_audio = np.zeros(16000, dtype=np.float32)
        except ImportError:
            # 如果numpy不可用，创建一个简单的列表
            test_audio = [0.0] * 16000

        # 尝试转录测试音频
        result = model.transcribe(test_audio, verbose=False)

        # 检查结果是否有效
        success = isinstance(result, dict) and "segments" in result
        if success:
            print(f"   ✅ {device.upper()} 设备测试通过")
        return success

    except Exception as e:
        print(f"   ❌ {device.upper()} 设备测试失败: {str(e)[:50]}...")
        return False


def transcribe_with_whisper_model(model, audio_path, task="transcribe", language="en"):
    """使用加载的Whisper模型进行转录"""
    try:
        print(f"🎯 开始{task}任务...")

        # 使用模型进行转录
        result = model.transcribe(
            audio_path, task=task, language=language, verbose=False
        )

        return result

    except Exception as e:
        print(f"❌ Whisper转录失败: {e}")
        raise


def write_srt_from_result(result, output_path):
    """将Whisper结果写入SRT文件"""
    with open(output_path, "w", encoding="utf-8") as f:
        for i, segment in enumerate(result["segments"], 1):
            start = segment["start"]
            end = segment["end"]
            text = segment["text"].strip()

            # 转换时间格式
            start_time = format_timestamp(start)
            end_time = format_timestamp(end)

            f.write(f"{i}\n")
            f.write(f"{start_time} --> {end_time}\n")
            f.write(f"{text}\n\n")


def format_timestamp(seconds):
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)

    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"


def ensure_dirs():
    os.makedirs(SUBTITLE_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)


def apply_keyword_highlighting(text, keywords):
    """对文本应用关键词高亮，支持不同优先级的关键词"""
    highlighted_text = text

    # 高优先级关键词（红色加粗）
    high_priority_keywords = [
        "AI",
        "NVIDIA",
        "artificial intelligence",
        "machine learning",
        "deep learning",
    ]

    # 中优先级关键词（绿色加粗）
    medium_priority_keywords = [
        "future",
        "innovation",
        "technology",
        "GPU",
        "computing",
    ]

    # 应用高优先级关键词高亮
    for kw in high_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['high_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )

    # 应用中优先级关键词高亮
    for kw in medium_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )

    # 应用其他关键词的普通高亮
    other_keywords = [
        kw
        for kw in keywords
        if kw.lower()
        not in [k.lower() for k in high_priority_keywords + medium_priority_keywords]
    ]

    for kw in other_keywords:
        highlighted_text = re.sub(
            rf"\b({re.escape(kw)})\b",
            rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
            highlighted_text,
            flags=re.IGNORECASE,
        )

    return highlighted_text


def transcribe_whisper_bilingual_optimized(video_path, model=None, device="cpu"):
    """使用预加载的Whisper模型生成英文字幕和中文翻译（优化版）"""
    base_name = os.path.splitext(os.path.basename(video_path))[0]

    # 如果没有传入模型，则加载新模型
    if model is None:
        model, device = load_whisper_model("large", device)

    # 定义文件路径
    english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")
    chinese_srt = os.path.join(SUBTITLE_DIR, base_name + "_chinese.srt")

    try:
        # 生成英文字幕
        print(f"🎯 生成英文字幕... (使用{device.upper()})")
        english_result = transcribe_with_whisper_model(
            model, video_path, task="transcribe", language="en"
        )
        write_srt_from_result(english_result, english_srt)
        print(f"✅ 英文字幕已保存: {os.path.basename(english_srt)}")

        # 生成中文翻译字幕
        print(f"🌏 生成中文翻译字幕... (使用{device.upper()})")
        chinese_result = transcribe_with_whisper_model(
            model, video_path, task="translate", language="en"
        )
        write_srt_from_result(chinese_result, chinese_srt)
        print(f"✅ 中文字幕已保存: {os.path.basename(chinese_srt)}")

        return english_srt, chinese_srt

    except Exception as e:
        print(f"❌ Whisper转录失败: {e}")
        raise


def transcribe_whisper_bilingual_fallback(video_path):
    """回退方案：使用命令行版本的Whisper"""
    base_name = os.path.splitext(os.path.basename(video_path))[0]

    print(f"🔄 使用命令行Whisper作为回退方案...")

    # 生成英文字幕
    print(f"🎯 生成英文字幕...")
    subprocess.run(
        [
            "whisper",
            video_path,
            "--model",
            "large",
            "--language",
            "English",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )

    # 生成中文翻译字幕
    print(f"🌏 生成中文翻译字幕...")
    subprocess.run(
        [
            "whisper",
            video_path,
            "--model",
            "large",
            "--task",
            "translate",
            "--language",
            "chinese",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )

    # 定义文件路径
    temp_srt = os.path.join(SUBTITLE_DIR, base_name + ".srt")
    english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")
    chinese_srt = os.path.join(SUBTITLE_DIR, base_name + "_chinese.srt")

    # 将英文字幕重命名保存
    if os.path.exists(temp_srt):
        os.rename(temp_srt, english_srt)
        print(f"✅ 英文字幕已保存: {os.path.basename(english_srt)}")

    # 重新生成中文翻译字幕（因为上面的翻译命令会覆盖原文件）
    print(f"🌏 重新生成中文翻译字幕...")
    subprocess.run(
        [
            "whisper",
            video_path,
            "--model",
            "large",
            "--task",
            "translate",
            "--language",
            "English",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )

    # 将中文翻译重命名保存
    if os.path.exists(temp_srt):
        os.rename(temp_srt, chinese_srt)
        print(f"✅ 中文字幕已保存: {os.path.basename(chinese_srt)}")

    return english_srt, chinese_srt


def merge_bilingual_srt(english_srt, chinese_srt):
    """合并英文和中文字幕文件为双语字幕"""
    with open(english_srt, "r", encoding="utf-8") as f:
        english_blocks = f.read().strip().split("\n\n")

    with open(chinese_srt, "r", encoding="utf-8") as f:
        chinese_blocks = f.read().strip().split("\n\n")

    merged_blocks = []

    # 确保两个字幕文件有相同数量的块
    min_blocks = min(len(english_blocks), len(chinese_blocks))

    for i in range(min_blocks):
        eng_lines = english_blocks[i].strip().split("\n")
        chi_lines = chinese_blocks[i].strip().split("\n")

        if len(eng_lines) >= 3 and len(chi_lines) >= 3:
            # 使用英文字幕的时间码
            index = eng_lines[0]
            timecode = eng_lines[1]
            english_text = " ".join(eng_lines[2:])
            chinese_text = " ".join(chi_lines[2:])

            # 合并为双语字幕
            merged_block = f"{index}\n{timecode}\n{english_text}\n{chinese_text}\n"
            merged_blocks.append(merged_block)

    # 保存合并后的字幕
    base_name = os.path.splitext(english_srt)[0]
    merged_path = base_name + "_bilingual.srt"

    with open(merged_path, "w", encoding="utf-8") as f:
        f.write("\n".join(merged_blocks))

    return merged_path


def bilingual_srt_to_ass(bilingual_srt, ass_path, keywords):
    """将双语字幕转换为ASS格式，支持关键词高亮"""
    with open(bilingual_srt, "r", encoding="utf-8") as f:
        srt_data = f.read()

    dialogues = []
    for block in srt_data.strip().split("\n\n"):
        lines = block.split("\n")
        if len(lines) < 4:  # 双语字幕至少需要4行：序号、时间、英文、中文
            continue

        start, end = lines[1].split(" --> ")
        english_text = lines[2]
        chinese_text = lines[3]

        # 对英文字幕应用关键词高亮
        highlighted_english = apply_keyword_highlighting(english_text, keywords)

        # 时间格式转换
        start = start.replace(",", ".")
        end = end.replace(",", ".")

        # 创建英文字幕对话行（上方显示）
        english_dialogue = (
            f"Dialogue: 0,{start},{end},English,,0,0,0,,{highlighted_english}"
        )
        dialogues.append(english_dialogue)

        # 创建中文字幕对话行（下方显示）
        chinese_dialogue = f"Dialogue: 0,{start},{end},Chinese,,0,0,0,,{chinese_text}"
        dialogues.append(chinese_dialogue)

    # 改进的ASS样式头部，支持双语显示
    header = """[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: English,Arial,24,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,8,10,10,60,1
Style: Chinese,Microsoft YaHei,22,&H00FFFF00,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,20,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
    with open(ass_path, "w", encoding="utf-8") as f:
        f.write(header + "\n".join(dialogues))


def convert_ass_to_srt(ass_path, srt_path):
    """将ASS字幕转换为SRT格式（去除样式标签）"""
    try:
        with open(ass_path, "r", encoding="utf-8") as f:
            ass_content = f.read()

        # 提取对话行
        dialogue_lines = []
        for line in ass_content.split("\n"):
            if line.startswith("Dialogue:"):
                dialogue_lines.append(line)

        srt_blocks = []
        subtitle_index = 1

        for dialogue in dialogue_lines:
            # 解析ASS对话行格式
            # Dialogue: Layer,Start,End,Style,Name,MarginL,MarginR,MarginV,Effect,Text
            parts = dialogue.split(",", 9)
            if len(parts) >= 10:
                start_time = parts[1]
                end_time = parts[2]
                text = parts[9]

                # 清理ASS样式标签
                text = re.sub(r"\{[^}]*\}", "", text)  # 移除所有ASS样式标签
                text = text.strip()

                if text:  # 只有非空文本才添加
                    # 转换时间格式从ASS到SRT
                    start_srt = convert_ass_time_to_srt(start_time)
                    end_srt = convert_ass_time_to_srt(end_time)

                    srt_block = f"{subtitle_index}\n{start_srt} --> {end_srt}\n{text}\n"
                    srt_blocks.append(srt_block)
                    subtitle_index += 1

        # 写入SRT文件
        with open(srt_path, "w", encoding="utf-8") as f:
            f.write("\n".join(srt_blocks))

        print(f"✅ ASS转SRT完成: {os.path.basename(srt_path)}")

    except Exception as e:
        print(f"❌ ASS转SRT失败: {e}")
        raise


def convert_ass_time_to_srt(ass_time):
    """将ASS时间格式转换为SRT时间格式"""
    # ASS格式: H:MM:SS.CC (小时:分钟:秒.厘秒)
    # SRT格式: HH:MM:SS,mmm (小时:分钟:秒,毫秒)

    try:
        # 分割时间部分
        time_parts = ass_time.split(":")
        hours = int(time_parts[0])
        minutes = int(time_parts[1])
        seconds_and_centiseconds = time_parts[2].split(".")
        seconds = int(seconds_and_centiseconds[0])
        centiseconds = (
            int(seconds_and_centiseconds[1]) if len(seconds_and_centiseconds) > 1 else 0
        )

        # 转换为毫秒
        milliseconds = centiseconds * 10

        # 格式化为SRT时间
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

    except (ValueError, IndexError):
        # 如果转换失败，返回默认时间
        return "00:00:00,000"


def merge_video_with_ass(video_path, ass_path):
    """合成视频和字幕，支持多种方法"""
    filename = os.path.splitext(os.path.basename(video_path))[0]
    output_path = os.path.join(OUTPUT_DIR, filename + "_final.mp4")

    # 方法1: 尝试使用subtitles滤镜（需要libass支持）
    try:
        print(f"🎬 尝试使用subtitles滤镜合成视频...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",
                "-i",
                video_path,
                "-vf",
                f"subtitles={ass_path}",
                "-c:a",
                "copy",
                output_path,
            ],
            capture_output=True,
            text=True,
            check=True,
        )
        print(f"✅ 合成完成: {output_path}")
        return
    except subprocess.CalledProcessError as e:
        print(f"⚠️  subtitles滤镜失败，尝试备用方法...")
        print(f"   错误信息: {e.stderr}")

    # 方法2: 使用SRT字幕作为备用方案
    try:
        # 生成SRT版本的字幕
        srt_path = ass_path.replace(".ass", ".srt")
        convert_ass_to_srt(ass_path, srt_path)

        print(f"🎬 使用SRT字幕合成视频...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",
                "-i",
                video_path,
                "-i",
                srt_path,
                "-c:v",
                "copy",
                "-c:a",
                "copy",
                "-c:s",
                "mov_text",
                "-metadata:s:s:0",
                "language=eng",
                output_path,
            ],
            check=True,
        )
        print(f"✅ 合成完成 (使用SRT字幕): {output_path}")
        return
    except subprocess.CalledProcessError as e:
        print(f"⚠️  SRT字幕方法也失败，使用基础合成...")

    # 方法3: 基础视频复制（无字幕烧录）
    try:
        print(f"🎬 创建无烧录字幕的视频副本...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",
                "-i",
                video_path,
                "-c",
                "copy",
                output_path,
            ],
            check=True,
        )
        print(f"✅ 视频复制完成: {output_path}")
        print(f"📝 字幕文件已保存: {ass_path}")
        print(f"💡 提示: 可以使用支持ASS字幕的播放器（如VLC）来播放带字幕的视频")
    except subprocess.CalledProcessError as e:
        print(f"❌ 所有方法都失败了: {e}")
        raise


def process_all_videos():
    """处理所有视频，生成高质量双语字幕（MPS优化版）"""
    ensure_dirs()

    # 获取所有视频文件
    video_files = glob(os.path.join(VIDEO_DIR, "*.mp4"))
    total_videos = len(video_files)

    if total_videos == 0:
        print(f"❌ 在 {VIDEO_DIR} 目录中没有找到MP4视频文件")
        return

    # 显示系统信息
    system_info = get_system_info()
    device = detect_device()

    print(f"💻 系统信息: {system_info}")
    print(f"🔧 计算设备: {device.upper()}")
    print(f"📁 找到 {total_videos} 个视频文件")
    print(f"🎯 关键词高亮: {len(KEYWORDS)} 个关键词")
    print("=" * 60)

    # 预加载Whisper模型（一次性加载，重复使用）
    model = None
    try:
        if WHISPER_AVAILABLE:
            print(f"📦 预加载Whisper模型...")
            model, device = load_whisper_model("large", device)
            print(f"✅ 模型加载完成，将使用 {device.upper()} 加速")
        else:
            print(f"⚠️  将使用命令行Whisper作为回退方案")
    except Exception as e:
        print(f"⚠️  模型加载失败，将使用命令行Whisper: {e}")
        model = None

    processed_count = 0
    failed_count = 0

    for i, video in enumerate(video_files, 1):
        try:
            print(f"\n🎬 [{i}/{total_videos}] 处理视频: {os.path.basename(video)}")

            # 使用预加载的模型或回退方案生成双语字幕
            if model is not None:
                english_srt, chinese_srt = transcribe_whisper_bilingual_optimized(
                    video, model, device
                )
            else:
                english_srt, chinese_srt = transcribe_whisper_bilingual_fallback(video)

            # 合并双语字幕
            print(f"🔗 合并双语字幕...")
            bilingual_srt = merge_bilingual_srt(english_srt, chinese_srt)

            # 转换为ASS格式并应用关键词高亮
            base = os.path.splitext(os.path.basename(video))[0]
            ass_path = os.path.join(SUBTITLE_DIR, base + "_bilingual.ass")
            print(f"✨ 生成ASS字幕文件...")
            bilingual_srt_to_ass(bilingual_srt, ass_path, KEYWORDS)

            # 合成最终视频
            print(f"🎥 合成最终视频...")
            merge_video_with_ass(video, ass_path)

            processed_count += 1
            print(f"✅ 视频处理完成: {os.path.basename(video)}")

        except Exception as e:
            failed_count += 1
            print(f"❌ 处理视频失败: {os.path.basename(video)}")
            print(f"   错误信息: {str(e)}")

    # 显示处理统计
    print("\n" + "=" * 60)
    print(f"📊 处理完成统计:")
    print(f"   💻 系统: {system_info}")
    print(f"   🔧 设备: {device.upper()}")
    print(f"   ✅ 成功处理: {processed_count} 个视频")
    print(f"   ❌ 处理失败: {failed_count} 个视频")
    print(f"   📁 输出目录: {OUTPUT_DIR}")
    print("=" * 60)


# 分阶段运行函数
def run_stage_1_transcribe(video_file=None):
    """阶段1: 仅生成双语字幕文件（MPS优化版）"""
    ensure_dirs()

    if video_file:
        video_files = [video_file] if os.path.exists(video_file) else []
    else:
        video_files = glob(os.path.join(VIDEO_DIR, "*.mp4"))

    if not video_files:
        print("❌ 没有找到视频文件")
        return

    # 预加载模型
    model = None
    device = detect_device()
    try:
        if WHISPER_AVAILABLE:
            model, device = load_whisper_model("large", device)
    except Exception as e:
        print(f"⚠️  模型加载失败，将使用命令行Whisper: {e}")

    for video in video_files:
        print(f"\n🎬 阶段1 - 转录字幕: {os.path.basename(video)}")
        try:
            if model is not None:
                english_srt, chinese_srt = transcribe_whisper_bilingual_optimized(
                    video, model, device
                )
            else:
                english_srt, chinese_srt = transcribe_whisper_bilingual_fallback(video)
            print(f"✅ 字幕生成完成:")
            print(f"   英文字幕: {english_srt}")
            print(f"   中文字幕: {chinese_srt}")
        except Exception as e:
            print(f"❌ 转录失败: {e}")


def run_stage_2_merge(video_file=None):
    """阶段2: 合并双语字幕"""
    if video_file:
        base_name = os.path.splitext(os.path.basename(video_file))[0]
        english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")
        chinese_srt = os.path.join(SUBTITLE_DIR, base_name + "_chinese.srt")

        if not (os.path.exists(english_srt) and os.path.exists(chinese_srt)):
            print(f"❌ 找不到字幕文件: {english_srt} 或 {chinese_srt}")
            return

        video_files = [(video_file, english_srt, chinese_srt)]
    else:
        # 自动查找所有字幕文件
        video_files = []
        for video in glob(os.path.join(VIDEO_DIR, "*.mp4")):
            base_name = os.path.splitext(os.path.basename(video))[0]
            english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")
            chinese_srt = os.path.join(SUBTITLE_DIR, base_name + "_chinese.srt")

            if os.path.exists(english_srt) and os.path.exists(chinese_srt):
                video_files.append((video, english_srt, chinese_srt))

    if not video_files:
        print("❌ 没有找到可合并的字幕文件")
        return

    for video, english_srt, chinese_srt in video_files:
        print(f"\n🔗 阶段2 - 合并字幕: {os.path.basename(video)}")
        try:
            bilingual_srt = merge_bilingual_srt(english_srt, chinese_srt)
            print(f"✅ 双语字幕合并完成: {bilingual_srt}")
        except Exception as e:
            print(f"❌ 合并失败: {e}")


def run_stage_3_ass(video_file=None):
    """阶段3: 生成ASS格式字幕"""
    if video_file:
        base_name = os.path.splitext(os.path.basename(video_file))[0]
        bilingual_srt = os.path.join(SUBTITLE_DIR, base_name + "_english_bilingual.srt")

        if not os.path.exists(bilingual_srt):
            print(f"❌ 找不到双语字幕文件: {bilingual_srt}")
            return

        video_files = [(video_file, bilingual_srt)]
    else:
        # 自动查找所有双语字幕文件
        video_files = []
        for video in glob(os.path.join(VIDEO_DIR, "*.mp4")):
            base_name = os.path.splitext(os.path.basename(video))[0]
            bilingual_srt = os.path.join(
                SUBTITLE_DIR, base_name + "_english_bilingual.srt"
            )

            if os.path.exists(bilingual_srt):
                video_files.append((video, bilingual_srt))

    if not video_files:
        print("❌ 没有找到双语字幕文件")
        return

    for video, bilingual_srt in video_files:
        print(f"\n✨ 阶段3 - 生成ASS字幕: {os.path.basename(video)}")
        try:
            base = os.path.splitext(os.path.basename(video))[0]
            ass_path = os.path.join(SUBTITLE_DIR, base + "_bilingual.ass")
            bilingual_srt_to_ass(bilingual_srt, ass_path, KEYWORDS)
            print(f"✅ ASS字幕生成完成: {ass_path}")
        except Exception as e:
            print(f"❌ ASS生成失败: {e}")


def run_stage_4_merge_video(video_file=None):
    """阶段4: 合成最终视频"""
    if video_file:
        base_name = os.path.splitext(os.path.basename(video_file))[0]
        ass_path = os.path.join(SUBTITLE_DIR, base_name + "_bilingual.ass")

        if not os.path.exists(ass_path):
            print(f"❌ 找不到ASS字幕文件: {ass_path}")
            return

        video_files = [(video_file, ass_path)]
    else:
        # 自动查找所有ASS字幕文件
        video_files = []
        for video in glob(os.path.join(VIDEO_DIR, "*.mp4")):
            base_name = os.path.splitext(os.path.basename(video))[0]
            ass_path = os.path.join(SUBTITLE_DIR, base_name + "_bilingual.ass")

            if os.path.exists(ass_path):
                video_files.append((video, ass_path))

    if not video_files:
        print("❌ 没有找到ASS字幕文件")
        return

    for video, ass_path in video_files:
        print(f"\n🎥 阶段4 - 合成视频: {os.path.basename(video)}")
        try:
            merge_video_with_ass(video, ass_path)
        except Exception as e:
            print(f"❌ 视频合成失败: {e}")


def main():
    """主函数 - 支持命令行参数"""
    parser = argparse.ArgumentParser(
        description="高质量双语视频字幕生成工具 (MPS加速版)"
    )
    parser.add_argument(
        "--stage",
        type=int,
        choices=[1, 2, 3, 4],
        help="运行指定阶段: 1=转录, 2=合并字幕, 3=生成ASS, 4=合成视频",
    )
    parser.add_argument("--video", type=str, help="指定视频文件路径 (相对于当前目录)")
    parser.add_argument("--all", action="store_true", help="运行完整流程 (默认)")

    args = parser.parse_args()

    print("🚀 高质量双语视频字幕生成工具 v2.1 (MPS加速版)")
    print("=" * 60)

    if args.stage == 1:
        run_stage_1_transcribe(args.video)
    elif args.stage == 2:
        run_stage_2_merge(args.video)
    elif args.stage == 3:
        run_stage_3_ass(args.video)
    elif args.stage == 4:
        run_stage_4_merge_video(args.video)
    else:
        # 默认运行完整流程
        process_all_videos()


if __name__ == "__main__":
    main()
