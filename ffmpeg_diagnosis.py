#!/usr/bin/env python3
"""
FFmpeg字幕支持诊断工具
帮助诊断和解决FFmpeg字幕滤镜问题
"""

import subprocess
import sys
import platform

def check_ffmpeg_version():
    """检查FFmpeg版本"""
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            version_line = lines[0] if lines else "未知版本"
            print(f"✅ FFmpeg版本: {version_line}")
            return True
        else:
            print("❌ FFmpeg未正确安装")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg未找到，请先安装FFmpeg")
        return False

def check_subtitle_filters():
    """检查字幕相关滤镜支持"""
    try:
        result = subprocess.run(["ffmpeg", "-filters"], capture_output=True, text=True)
        if result.returncode == 0:
            filters = result.stdout
            
            # 检查关键滤镜
            subtitle_filters = ["subtitles", "ass", "drawtext"]
            supported_filters = []
            missing_filters = []
            
            for filter_name in subtitle_filters:
                if f" {filter_name} " in filters or f"\n{filter_name} " in filters:
                    supported_filters.append(filter_name)
                else:
                    missing_filters.append(filter_name)
            
            print(f"\n📋 字幕滤镜支持检查:")
            for filter_name in supported_filters:
                print(f"✅ {filter_name} 滤镜: 支持")
            
            for filter_name in missing_filters:
                print(f"❌ {filter_name} 滤镜: 不支持")
            
            return len(missing_filters) == 0
        
    except Exception as e:
        print(f"❌ 检查滤镜时出错: {e}")
        return False

def check_libass_support():
    """检查libass库支持"""
    try:
        # 尝试使用subtitles滤镜
        result = subprocess.run(
            ["ffmpeg", "-f", "lavfi", "-i", "testsrc=duration=1:size=320x240:rate=1", 
             "-vf", "subtitles=filename=nonexistent.srt", "-f", "null", "-"],
            capture_output=True, text=True
        )
        
        if "No such filter: 'subtitles'" in result.stderr:
            print("❌ libass支持: 未编译")
            return False
        elif "No such file or directory" in result.stderr:
            print("✅ libass支持: 已编译 (字幕滤镜可用)")
            return True
        else:
            print("⚠️  libass支持: 状态不明")
            return False
            
    except Exception as e:
        print(f"❌ 检查libass时出错: {e}")
        return False

def get_installation_instructions():
    """获取针对不同系统的安装说明"""
    system = platform.system().lower()
    
    print(f"\n🔧 针对 {platform.system()} 的解决方案:")
    
    if system == "darwin":  # macOS
        print("""
macOS 解决方案:
1. 使用Homebrew重新安装FFmpeg (推荐):
   brew uninstall ffmpeg
   brew install ffmpeg

2. 或者安装完整版本:
   brew install ffmpeg --with-libass

3. 如果仍有问题，尝试:
   brew install libass
   brew reinstall ffmpeg
""")
    
    elif system == "linux":
        print("""
Linux 解决方案:
1. Ubuntu/Debian:
   sudo apt update
   sudo apt install ffmpeg libass-dev
   
2. 如果仍有问题，从源码编译:
   sudo apt install build-essential libass-dev
   # 然后下载并编译FFmpeg源码

3. CentOS/RHEL:
   sudo yum install epel-release
   sudo yum install ffmpeg libass-devel
""")
    
    elif system == "windows":
        print("""
Windows 解决方案:
1. 下载完整版FFmpeg:
   - 访问 https://ffmpeg.org/download.html
   - 下载 "full" 版本而不是 "essentials"
   
2. 或使用包管理器:
   # 使用Chocolatey
   choco install ffmpeg-full
   
   # 使用Scoop
   scoop install ffmpeg
""")

def suggest_alternatives():
    """建议替代方案"""
    print(f"\n💡 替代方案:")
    print("""
1. 使用外部字幕文件:
   - 生成的ASS/SRT字幕文件可以与视频分开使用
   - 支持ASS字幕的播放器: VLC, MPV, PotPlayer等

2. 在线字幕烧录服务:
   - 使用在线工具将字幕烧录到视频中

3. 使用其他工具:
   - Aegisub: 专业字幕编辑软件
   - HandBrake: 视频转换工具，支持字幕烧录
""")

def main():
    """主诊断流程"""
    print("🔍 FFmpeg字幕支持诊断工具")
    print("=" * 50)
    
    # 检查FFmpeg
    if not check_ffmpeg_version():
        print("\n❌ 请先安装FFmpeg")
        get_installation_instructions()
        return
    
    # 检查滤镜支持
    filters_ok = check_subtitle_filters()
    
    # 检查libass支持
    libass_ok = check_libass_support()
    
    print(f"\n📊 诊断结果:")
    print(f"FFmpeg: ✅ 已安装")
    print(f"字幕滤镜: {'✅ 支持' if filters_ok else '❌ 不支持'}")
    print(f"libass库: {'✅ 支持' if libass_ok else '❌ 不支持'}")
    
    if not (filters_ok and libass_ok):
        print(f"\n⚠️  检测到字幕支持问题")
        get_installation_instructions()
        suggest_alternatives()
        
        print(f"\n🔄 修复后的测试:")
        print("1. 重新安装FFmpeg后，再次运行此诊断脚本")
        print("2. 或者直接运行主处理脚本，它会自动使用备用方法")
    else:
        print(f"\n✅ FFmpeg字幕支持正常！可以正常使用字幕烧录功能")

if __name__ == "__main__":
    main()
