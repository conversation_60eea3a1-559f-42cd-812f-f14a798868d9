#!/usr/bin/env python3
"""
关键词高亮功能测试脚本
用于测试关键词高亮功能是否正常工作
"""

import re

# 从主脚本导入配置
KEYWORDS = [
    "AI", "NVIDIA", "future", "students", "learning", "innovation",
    "technology", "artificial intelligence", "machine learning", 
    "deep learning", "GPU", "computing", "data", "algorithm",
    "neural network", "robotics", "automation", "digital transformation"
]

KEYWORD_STYLES = {
    "high_priority": r"{\\fs28\\c&HFF0000&\\b1}",  # 红色加粗
    "medium_priority": r"{\\fs26\\c&H00FF00&\\b1}",  # 绿色加粗
    "normal": r"{\\fs24\\c&HFFFFFF&\\b0}"  # 白色正常
}

def apply_keyword_highlighting(text, keywords):
    """对文本应用关键词高亮，支持不同优先级的关键词"""
    highlighted_text = text
    
    # 高优先级关键词（红色加粗）
    high_priority_keywords = [
        "AI", "NVIDIA", "artificial intelligence", "machine learning", "deep learning"
    ]
    
    # 中优先级关键词（绿色加粗）
    medium_priority_keywords = [
        "future", "innovation", "technology", "GPU", "computing"
    ]
    
    # 应用高优先级关键词高亮
    for kw in high_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['high_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )
    
    # 应用中优先级关键词高亮
    for kw in medium_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )
    
    # 应用其他关键词的普通高亮
    other_keywords = [kw for kw in keywords if kw.lower() not in 
                     [k.lower() for k in high_priority_keywords + medium_priority_keywords]]
    
    for kw in other_keywords:
        highlighted_text = re.sub(
            rf"\b({re.escape(kw)})\b",
            rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
            highlighted_text,
            flags=re.IGNORECASE,
        )
    
    return highlighted_text

def test_keyword_highlighting():
    """测试关键词高亮功能"""
    test_sentences = [
        "NVIDIA is leading the AI revolution with GPU computing.",
        "Machine learning and deep learning are transforming the future.",
        "Students are learning about artificial intelligence and innovation.",
        "The technology behind neural network automation is amazing.",
        "Digital transformation requires advanced computing and data processing."
    ]
    
    print("🧪 关键词高亮测试")
    print("=" * 60)
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n测试 {i}: {sentence}")
        highlighted = apply_keyword_highlighting(sentence, KEYWORDS)
        print(f"高亮后: {highlighted}")
        
        # 统计高亮的关键词数量
        high_priority_count = highlighted.count(KEYWORD_STYLES['high_priority'])
        medium_priority_count = highlighted.count(KEYWORD_STYLES['medium_priority'])
        
        print(f"高优先级关键词: {high_priority_count} 个")
        print(f"中优先级关键词: {medium_priority_count} 个")
    
    print("\n" + "=" * 60)
    print("✅ 关键词高亮测试完成")

if __name__ == "__main__":
    test_keyword_highlighting()
