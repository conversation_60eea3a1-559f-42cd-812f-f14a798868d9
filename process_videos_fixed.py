#!/usr/bin/env python3
"""
高质量双语视频字幕生成工具 (修复版)

功能特点:
- 使用Whisper large模型进行高质量语音转录
- 使用Google Translate API生成真正的中文翻译
- 支持关键词高亮（不同优先级，不同颜色）
- 生成ASS格式字幕，支持丰富的样式
- 自动合成带字幕的最终视频
- 支持分阶段运行

修复内容:
- 修复中文翻译问题：使用真正的翻译服务而不是Whisper的translate任务
- 添加多种翻译服务支持：Google Translate, 百度翻译等

使用方法:
1. 将MP4视频文件放入 'videos' 目录
2. 运行脚本: python process_videos_fixed.py
3. 处理完成的视频将保存在 'output' 目录

依赖要求:
- whisper (pip install openai-whisper)
- googletrans (pip install googletrans==4.0.0rc1)
- ffmpeg (系统安装)

作者: AI Assistant
版本: 2.2 (修复版)
"""

import os
import re
import subprocess
import argparse
import time
from glob import glob

# 导入翻译库
try:
    from translate import Translator

    TRANSLATE_AVAILABLE = True
except ImportError:
    TRANSLATE_AVAILABLE = False
    print("⚠️  translate未安装，请运行: pip install translate")

try:
    import requests

    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("⚠️  requests未安装，请运行: pip install requests")

VIDEO_DIR = "videos"
SUBTITLE_DIR = "subtitles"
OUTPUT_DIR = "output"

# FFmpeg路径配置 - 优先使用Homebrew版本
FFMPEG_PATH = "/opt/homebrew/bin/ffmpeg"
if not os.path.exists(FFMPEG_PATH):
    FFMPEG_PATH = "ffmpeg"  # 回退到系统PATH中的ffmpeg

# 关键词配置 - 可以根据需要添加更多关键词
KEYWORDS = [
    "AI",
    "NVIDIA",
    "future",
    "students",
    "learning",
    "innovation",
    "technology",
    "artificial intelligence",
    "machine learning",
    "deep learning",
    "GPU",
    "computing",
    "data",
    "algorithm",
    "neural network",
    "robotics",
    "automation",
    "digital transformation",
]

# 关键词高亮样式配置
KEYWORD_STYLES = {
    "high_priority": r"{\\fs28\\c&HFF0000&\\b1}",  # 红色加粗
    "medium_priority": r"{\\fs26\\c&H00FF00&\\b1}",  # 绿色加粗
    "normal": r"{\\fs24\\c&HFFFFFF&\\b0}",  # 白色正常
}


def ensure_dirs():
    os.makedirs(SUBTITLE_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)


def transcribe_english_only(video_path):
    """使用Whisper生成英文字幕"""
    base_name = os.path.splitext(os.path.basename(video_path))[0]

    print(f"🎯 生成英文字幕 (使用Whisper large模型)...")
    subprocess.run(
        [
            "whisper",
            video_path,
            "--model",
            "large",
            "--language",
            "English",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )

    # 定义文件路径
    temp_srt = os.path.join(SUBTITLE_DIR, base_name + ".srt")
    english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")

    # 重命名英文字幕文件
    if os.path.exists(temp_srt):
        os.rename(temp_srt, english_srt)
        print(f"✅ 英文字幕已保存: {os.path.basename(english_srt)}")
        return english_srt
    else:
        raise FileNotFoundError(f"Whisper未生成字幕文件: {temp_srt}")


def translate_srt_to_chinese(english_srt):
    """将英文SRT字幕翻译为中文 - 使用OpenAI API优化版"""
    base_name = os.path.splitext(english_srt)[0]
    chinese_srt = base_name.replace("_english", "_chinese") + ".srt"

    print(f"🌏 使用OpenAI翻译英文字幕为中文...")

    # 读取英文字幕
    with open(english_srt, "r", encoding="utf-8") as f:
        english_content = f.read()

    # 检查OpenAI配置
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        print("❌ 未设置OPENAI_API_KEY环境变量，使用占位符翻译")
        return create_placeholder_translation(english_srt, chinese_srt)

    try:
        import openai

        # 初始化OpenAI客户端
        client = openai.OpenAI(
            api_key=openai_api_key,
            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
        )

        # 使用批量翻译提高效率
        translated_content = translate_srt_with_openai_batch(client, english_content)

        # 保存翻译结果
        with open(chinese_srt, "w", encoding="utf-8") as f:
            f.write(translated_content)

        print(f"✅ OpenAI翻译完成: {os.path.basename(chinese_srt)}")
        return chinese_srt

    except ImportError:
        print("❌ openai库未安装，请运行: pip install openai")
        return create_placeholder_translation(english_srt, chinese_srt)
    except Exception as e:
        print(f"❌ OpenAI翻译失败: {e}")
        return create_placeholder_translation(english_srt, chinese_srt)


def translate_srt_with_openai_batch(client, srt_content):
    """使用OpenAI批量翻译SRT内容 - 参考Go版本的prompt设计"""

    # Go版本的高质量翻译prompt
    system_prompt = """你是一个专业的字幕翻译专家，专注于将英文字幕准确翻译为中文。请遵循以下要求：

1. 保持SRT字幕的格式完全不变（序号、时间轴、空行分隔）
2. 只翻译字幕文本内容，不要修改序号和时间轴
3. 翻译要自然流畅，符合中文表达习惯
4. 保持原文的语气和情感色彩
5. 对于专业术语，使用准确的中文对应词汇
6. 确保翻译的完整性，不要遗漏任何内容
7. 保持每行字幕的长度适中，便于阅读

请直接输出翻译后的完整SRT文件内容，不要添加任何解释或说明。"""

    user_prompt = f"""请将以下英文SRT字幕翻译为中文，保持格式完全不变：

{srt_content}"""

    try:
        print("🤖 调用OpenAI API进行批量翻译...")

        response = client.chat.completions.create(
            model=os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            temperature=0.3,  # 较低的温度确保翻译一致性
            max_tokens=4000,  # 根据字幕长度调整
        )

        translated_content = response.choices[0].message.content.strip()

        # 验证翻译结果格式
        if validate_srt_format(translated_content):
            print("✅ 翻译格式验证通过")
            return translated_content
        else:
            print("⚠️  翻译格式验证失败，尝试修复...")
            return fix_srt_format(translated_content, srt_content)

    except Exception as e:
        print(f"❌ OpenAI API调用失败: {e}")
        raise


def translate_srt_with_openai_chunks(client, srt_content, chunk_size=10):
    """分块翻译SRT内容 - 适用于长字幕文件"""

    # 解析SRT块
    blocks = srt_content.strip().split("\n\n")
    total_blocks = len(blocks)

    if total_blocks <= chunk_size:
        # 如果块数较少，直接批量翻译
        return translate_srt_with_openai_batch(client, srt_content)

    print(f"📝 字幕较长({total_blocks}块)，使用分块翻译...")

    translated_blocks = []

    # 分块处理
    for i in range(0, total_blocks, chunk_size):
        chunk_blocks = blocks[i : i + chunk_size]
        chunk_content = "\n\n".join(chunk_blocks)

        print(
            f"   翻译块 {i//chunk_size + 1}/{(total_blocks + chunk_size - 1)//chunk_size}..."
        )

        # 翻译当前块
        chunk_translated = translate_srt_with_openai_batch(client, chunk_content)
        translated_blocks.append(chunk_translated)

        # 添加延迟避免API限制
        if i + chunk_size < total_blocks:
            time.sleep(1)

    return "\n\n".join(translated_blocks)


def validate_srt_format(content):
    """验证SRT格式是否正确"""
    try:
        blocks = content.strip().split("\n\n")

        for block in blocks:
            lines = block.strip().split("\n")
            if len(lines) < 3:
                return False

            # 检查序号
            if not lines[0].strip().isdigit():
                return False

            # 检查时间轴格式
            if "-->" not in lines[1]:
                return False

        return True

    except Exception:
        return False


def fix_srt_format(translated_content, original_content):
    """修复SRT格式问题"""
    try:
        # 解析原始和翻译内容
        original_blocks = original_content.strip().split("\n\n")
        translated_blocks = translated_content.strip().split("\n\n")

        fixed_blocks = []

        for i, original_block in enumerate(original_blocks):
            original_lines = original_block.strip().split("\n")

            if i < len(translated_blocks):
                translated_lines = translated_blocks[i].strip().split("\n")

                # 使用原始的序号和时间轴，翻译的文本
                if len(original_lines) >= 3 and len(translated_lines) >= 3:
                    fixed_block = f"{original_lines[0]}\n{original_lines[1]}\n"
                    # 提取翻译的文本部分
                    translated_text = "\n".join(translated_lines[2:])
                    fixed_block += translated_text
                    fixed_blocks.append(fixed_block)
                else:
                    # 如果格式有问题，使用占位符
                    english_text = "\n".join(original_lines[2:])
                    fixed_block = f"{original_lines[0]}\n{original_lines[1]}\n[翻译] {english_text}"
                    fixed_blocks.append(fixed_block)
            else:
                # 如果翻译块不够，使用占位符
                english_text = "\n".join(original_lines[2:])
                fixed_block = (
                    f"{original_lines[0]}\n{original_lines[1]}\n[翻译] {english_text}"
                )
                fixed_blocks.append(fixed_block)

        return "\n\n".join(fixed_blocks)

    except Exception as e:
        print(f"⚠️  格式修复失败: {e}")
        return translated_content


def create_placeholder_translation(english_srt, chinese_srt):
    """创建占位符翻译"""
    print("🔄 创建占位符中文翻译...")

    with open(english_srt, "r", encoding="utf-8") as f:
        english_content = f.read()

    # 解析SRT块并添加占位符
    blocks = english_content.strip().split("\n\n")
    translated_blocks = []

    for block in blocks:
        lines = block.strip().split("\n")
        if len(lines) >= 3:
            index = lines[0]
            timecode = lines[1]
            english_text = " ".join(lines[2:])
            chinese_text = f"[需要翻译] {english_text}"
            translated_block = f"{index}\n{timecode}\n{chinese_text}\n"
            translated_blocks.append(translated_block)

    # 保存占位符翻译
    with open(chinese_srt, "w", encoding="utf-8") as f:
        f.write("\n".join(translated_blocks))

    print(f"✅ 占位符翻译已保存: {os.path.basename(chinese_srt)}")
    return chinese_srt


def merge_bilingual_srt(english_srt, chinese_srt):
    """合并英文和中文字幕文件为双语字幕"""
    with open(english_srt, "r", encoding="utf-8") as f:
        english_blocks = f.read().strip().split("\n\n")

    with open(chinese_srt, "r", encoding="utf-8") as f:
        chinese_blocks = f.read().strip().split("\n\n")

    merged_blocks = []

    # 确保两个字幕文件有相同数量的块
    min_blocks = min(len(english_blocks), len(chinese_blocks))

    for i in range(min_blocks):
        eng_lines = english_blocks[i].strip().split("\n")
        chi_lines = chinese_blocks[i].strip().split("\n")

        if len(eng_lines) >= 3 and len(chi_lines) >= 3:
            # 使用英文字幕的时间码
            index = eng_lines[0]
            timecode = eng_lines[1]
            english_text = " ".join(eng_lines[2:])
            chinese_text = " ".join(chi_lines[2:])

            # 合并为双语字幕
            merged_block = f"{index}\n{timecode}\n{english_text}\n{chinese_text}\n"
            merged_blocks.append(merged_block)

    # 保存合并后的字幕
    base_name = os.path.splitext(english_srt)[0]
    merged_path = base_name + "_bilingual.srt"

    with open(merged_path, "w", encoding="utf-8") as f:
        f.write("\n".join(merged_blocks))

    return merged_path


def apply_keyword_highlighting(text, keywords):
    """对文本应用关键词高亮，支持不同优先级的关键词"""
    highlighted_text = text

    # 高优先级关键词（红色加粗）
    high_priority_keywords = [
        "AI",
        "NVIDIA",
        "artificial intelligence",
        "machine learning",
        "deep learning",
    ]

    # 中优先级关键词（绿色加粗）
    medium_priority_keywords = [
        "future",
        "innovation",
        "technology",
        "GPU",
        "computing",
    ]

    # 应用高优先级关键词高亮
    for kw in high_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['high_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )

    # 应用中优先级关键词高亮
    for kw in medium_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )

    # 应用其他关键词的普通高亮
    other_keywords = [
        kw
        for kw in keywords
        if kw.lower()
        not in [k.lower() for k in high_priority_keywords + medium_priority_keywords]
    ]

    for kw in other_keywords:
        highlighted_text = re.sub(
            rf"\b({re.escape(kw)})\b",
            rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
            highlighted_text,
            flags=re.IGNORECASE,
        )

    return highlighted_text


def bilingual_srt_to_ass(bilingual_srt, ass_path, keywords):
    """将双语字幕转换为ASS格式，支持关键词高亮"""
    with open(bilingual_srt, "r", encoding="utf-8") as f:
        srt_data = f.read()

    dialogues = []
    for block in srt_data.strip().split("\n\n"):
        lines = block.split("\n")
        if len(lines) < 4:  # 双语字幕至少需要4行：序号、时间、英文、中文
            continue

        start, end = lines[1].split(" --> ")
        english_text = lines[2]
        chinese_text = lines[3]

        # 对英文字幕应用关键词高亮
        highlighted_english = apply_keyword_highlighting(english_text, keywords)

        # 时间格式转换
        start = start.replace(",", ".")
        end = end.replace(",", ".")

        # 创建英文字幕对话行（上方显示）
        english_dialogue = (
            f"Dialogue: 0,{start},{end},English,,0,0,0,,{highlighted_english}"
        )
        dialogues.append(english_dialogue)

        # 创建中文字幕对话行（下方显示）
        chinese_dialogue = f"Dialogue: 0,{start},{end},Chinese,,0,0,0,,{chinese_text}"
        dialogues.append(chinese_dialogue)

    # 改进的ASS样式头部，支持双语显示
    header = """[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: English,Arial,24,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,8,10,10,60,1
Style: Chinese,Microsoft YaHei,22,&H00FFFF00,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,20,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
    with open(ass_path, "w", encoding="utf-8") as f:
        f.write(header + "\n".join(dialogues))


def merge_video_with_ass(video_path, ass_path):
    """合成视频和字幕，支持多种方法"""
    filename = os.path.splitext(os.path.basename(video_path))[0]
    output_path = os.path.join(OUTPUT_DIR, filename + "_final.mp4")

    # 方法1: 尝试使用subtitles滤镜（需要libass支持）
    try:
        print(f"🎬 尝试使用subtitles滤镜合成视频...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",
                "-i",
                video_path,
                "-vf",
                f"subtitles={ass_path}",
                "-c:a",
                "copy",
                output_path,
            ],
            capture_output=True,
            text=True,
            check=True,
        )
        print(f"✅ 合成完成: {output_path}")
        return
    except subprocess.CalledProcessError as e:
        print(f"⚠️  subtitles滤镜失败，尝试备用方法...")
        print(f"   错误信息: {e.stderr}")

    # 方法2: 基础视频复制（无字幕烧录）
    try:
        print(f"🎬 创建无烧录字幕的视频副本...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",
                "-i",
                video_path,
                "-c",
                "copy",
                output_path,
            ],
            check=True,
        )
        print(f"✅ 视频复制完成: {output_path}")
        print(f"📝 字幕文件已保存: {ass_path}")
        print(f"💡 提示: 可以使用支持ASS字幕的播放器（如VLC）来播放带字幕的视频")
    except subprocess.CalledProcessError as e:
        print(f"❌ 视频处理失败: {e}")
        raise


def process_all_videos():
    """处理所有视频，生成高质量双语字幕（修复版）"""
    ensure_dirs()

    # 获取所有视频文件
    video_files = glob(os.path.join(VIDEO_DIR, "*.mp4"))
    total_videos = len(video_files)

    if total_videos == 0:
        print(f"❌ 在 {VIDEO_DIR} 目录中没有找到MP4视频文件")
        return

    print(f"📁 找到 {total_videos} 个视频文件")
    print(f"🔧 使用Whisper large模型进行转录")
    print(f"🌏 使用Google Translate进行中文翻译")
    print(f"🎯 关键词高亮: {len(KEYWORDS)} 个关键词")
    print("=" * 60)

    processed_count = 0
    failed_count = 0

    for i, video in enumerate(video_files, 1):
        try:
            print(f"\n🎬 [{i}/{total_videos}] 处理视频: {os.path.basename(video)}")

            # 1. 生成英文字幕
            english_srt = transcribe_english_only(video)

            # 2. 翻译为中文字幕
            chinese_srt = translate_srt_to_chinese(english_srt)

            # 3. 合并双语字幕
            print(f"🔗 合并双语字幕...")
            bilingual_srt = merge_bilingual_srt(english_srt, chinese_srt)

            # 4. 转换为ASS格式并应用关键词高亮
            base = os.path.splitext(os.path.basename(video))[0]
            ass_path = os.path.join(SUBTITLE_DIR, base + "_bilingual.ass")
            print(f"✨ 生成ASS字幕文件...")
            bilingual_srt_to_ass(bilingual_srt, ass_path, KEYWORDS)

            # 5. 合成最终视频
            print(f"🎥 合成最终视频...")
            merge_video_with_ass(video, ass_path)

            processed_count += 1
            print(f"✅ 视频处理完成: {os.path.basename(video)}")

        except Exception as e:
            failed_count += 1
            print(f"❌ 处理视频失败: {os.path.basename(video)}")
            print(f"   错误信息: {str(e)}")

    # 显示处理统计
    print("\n" + "=" * 60)
    print(f"📊 处理完成统计:")
    print(f"   ✅ 成功处理: {processed_count} 个视频")
    print(f"   ❌ 处理失败: {failed_count} 个视频")
    print(f"   📁 输出目录: {OUTPUT_DIR}")
    print("=" * 60)


# 分阶段运行函数
def run_stage_1_transcribe(video_file=None):
    """阶段1: 仅生成英文字幕"""
    ensure_dirs()

    if video_file:
        video_files = [video_file] if os.path.exists(video_file) else []
    else:
        video_files = glob(os.path.join(VIDEO_DIR, "*.mp4"))

    if not video_files:
        print("❌ 没有找到视频文件")
        return

    for video in video_files:
        print(f"\n🎬 阶段1 - 转录英文字幕: {os.path.basename(video)}")
        try:
            english_srt = transcribe_english_only(video)
            print(f"✅ 英文字幕生成完成: {english_srt}")
        except Exception as e:
            print(f"❌ 转录失败: {e}")


def run_stage_2_translate(video_file=None):
    """阶段2: 翻译中文字幕"""
    if video_file:
        base_name = os.path.splitext(os.path.basename(video_file))[0]
        english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")

        if not os.path.exists(english_srt):
            print(f"❌ 找不到英文字幕文件: {english_srt}")
            return

        video_files = [(video_file, english_srt)]
    else:
        # 自动查找所有英文字幕文件
        video_files = []
        for video in glob(os.path.join(VIDEO_DIR, "*.mp4")):
            base_name = os.path.splitext(os.path.basename(video))[0]
            english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")

            if os.path.exists(english_srt):
                video_files.append((video, english_srt))

    if not video_files:
        print("❌ 没有找到英文字幕文件")
        return

    for video, english_srt in video_files:
        print(f"\n🌏 阶段2 - 翻译中文字幕: {os.path.basename(video)}")
        try:
            chinese_srt = translate_srt_to_chinese(english_srt)
            print(f"✅ 中文字幕翻译完成: {chinese_srt}")
        except Exception as e:
            print(f"❌ 翻译失败: {e}")


def main():
    """主函数 - 支持命令行参数"""
    parser = argparse.ArgumentParser(description="高质量双语视频字幕生成工具 (修复版)")
    parser.add_argument(
        "--stage",
        type=int,
        choices=[1, 2, 3, 4],
        help="运行指定阶段: 1=转录, 2=翻译, 3=生成ASS, 4=合成视频",
    )
    parser.add_argument("--video", type=str, help="指定视频文件路径 (相对于当前目录)")
    parser.add_argument("--all", action="store_true", help="运行完整流程 (默认)")

    args = parser.parse_args()

    print("🚀 高质量双语视频字幕生成工具 v2.2 (修复版)")
    print("=" * 60)

    if args.stage == 1:
        run_stage_1_transcribe(args.video)
    elif args.stage == 2:
        run_stage_2_translate(args.video)
    elif args.stage == 3:
        print("阶段3功能请参考原版脚本")
    elif args.stage == 4:
        print("阶段4功能请参考原版脚本")
    else:
        # 默认运行完整流程
        process_all_videos()


if __name__ == "__main__":
    main()
