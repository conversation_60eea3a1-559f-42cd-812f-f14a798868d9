#!/usr/bin/env python3
"""
使用示例脚本
演示如何使用改进后的视频处理工具
"""

import os
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否已安装"""
    print("🔍 检查依赖...")
    
    # 检查whisper
    try:
        result = subprocess.run(["whisper", "--help"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Whisper 已安装")
        else:
            print("❌ Whisper 未安装，请运行: pip install openai-whisper")
            return False
    except FileNotFoundError:
        print("❌ Whisper 未安装，请运行: pip install openai-whisper")
        return False
    
    # 检查ffmpeg
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg 已安装")
        else:
            print("❌ FFmpeg 未安装，请安装 FFmpeg")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg 未安装，请安装 FFmpeg")
        return False
    
    return True

def setup_directories():
    """设置必要的目录结构"""
    print("📁 设置目录结构...")
    
    directories = ["videos", "subtitles", "output"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ 目录已创建: {dir_name}/")

def show_usage_example():
    """显示使用示例"""
    print("\n" + "=" * 60)
    print("📖 使用示例")
    print("=" * 60)
    
    print("""
1. 将MP4视频文件放入 videos/ 目录
   例如: videos/nvidia_keynote.mp4

2. 运行主脚本:
   python process_videos.py

3. 处理完成后，查看输出:
   - subtitles/ 目录包含所有字幕文件
   - output/ 目录包含最终的带字幕视频

4. 文件结构示例:
   videos/
   ├── nvidia_keynote.mp4
   
   subtitles/
   ├── nvidia_keynote_english.srt      # 英文字幕
   ├── nvidia_keynote_chinese.srt      # 中文字幕
   ├── nvidia_keynote_bilingual.srt    # 双语字幕
   └── nvidia_keynote_bilingual.ass    # ASS格式字幕
   
   output/
   └── nvidia_keynote_final.mp4        # 最终视频
""")

def show_customization_tips():
    """显示自定义提示"""
    print("🎨 自定义提示")
    print("=" * 60)
    
    print("""
1. 添加自定义关键词:
   在 process_videos.py 中修改 KEYWORDS 列表

2. 调整关键词优先级:
   在 apply_keyword_highlighting() 函数中修改优先级列表

3. 更改字幕样式:
   修改 KEYWORD_STYLES 字典中的ASS样式代码

4. 使用不同的Whisper模型:
   将 "--model", "large" 改为 "medium", "base", 或 "small"
   
5. 调整字幕位置:
   修改ASS样式中的 Alignment 和 MarginV 参数
""")

def main():
    """主函数"""
    print("🚀 高质量双语视频字幕生成工具 - 使用示例")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 请先安装所需依赖")
        return
    
    # 设置目录
    setup_directories()
    
    # 显示使用示例
    show_usage_example()
    
    # 显示自定义提示
    show_customization_tips()
    
    print("\n🎯 准备就绪！现在可以:")
    print("1. 将MP4视频放入 videos/ 目录")
    print("2. 运行: python process_videos.py")
    print("\n✨ 享受高质量的双语字幕体验！")

if __name__ == "__main__":
    main()
