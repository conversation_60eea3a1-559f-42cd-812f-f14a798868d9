#!/usr/bin/env python3
"""
测试OpenAI翻译功能
"""

import os
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_openai_translation():
    """测试OpenAI翻译功能"""
    
    # 检查环境变量
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请设置OPENAI_API_KEY环境变量")
        print("   export OPENAI_API_KEY='your-api-key'")
        return False
    
    print("🔑 OpenAI API密钥已设置")
    
    # 检查是否有英文字幕文件
    english_srt = "subtitles/output_10s_english.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文字幕文件不存在: {english_srt}")
        print("   请先运行字幕生成脚本")
        return False
    
    print(f"📄 找到英文字幕文件: {english_srt}")
    
    try:
        # 导入翻译函数
        from process_videos_fixed import translate_srt_to_chinese
        
        print("🚀 开始OpenAI翻译测试...")
        
        # 执行翻译
        chinese_srt = translate_srt_to_chinese(english_srt)
        
        if os.path.exists(chinese_srt):
            print(f"✅ 翻译成功: {chinese_srt}")
            
            # 显示翻译结果预览
            print("\n📖 翻译结果预览:")
            with open(chinese_srt, "r", encoding="utf-8") as f:
                content = f.read()
            
            blocks = content.strip().split("\n\n")
            for i, block in enumerate(blocks[:3], 1):  # 只显示前3个块
                lines = block.split("\n")
                if len(lines) >= 3:
                    print(f"  {i}. {lines[1]}")
                    print(f"     中文: {lines[2]}")
                    print()
            
            if len(blocks) > 3:
                print(f"  ... 还有 {len(blocks) - 3} 个字幕块")
            
            return True
        else:
            print("❌ 翻译文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 翻译测试失败: {e}")
        return False

def test_openai_api_connection():
    """测试OpenAI API连接"""
    try:
        import openai
        
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 未设置OPENAI_API_KEY")
            return False
        
        client = openai.OpenAI(
            api_key=api_key,
            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        )
        
        print("🧪 测试OpenAI API连接...")
        
        # 简单的API测试
        response = client.chat.completions.create(
            model=os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
            messages=[
                {"role": "user", "content": "Hello, this is a test. Please respond with 'API connection successful'."}
            ],
            max_tokens=50
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ API连接成功: {result}")
        return True
        
    except ImportError:
        print("❌ openai库未安装，请运行: pip install openai")
        return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def create_sample_english_srt():
    """创建示例英文字幕文件用于测试"""
    sample_content = """1
00:00:00,000 --> 00:00:03,000
customization going hand in hand

2
00:00:03,000 --> 00:00:04,120
and what makes it possible

3
00:00:04,120 --> 00:00:06,180
is artificial intelligence and robotics

4
00:00:06,180 --> 00:00:07,880
I can't be more delighted to work

5
00:00:07,880 --> 00:00:10,000
with all of the great people at BMW"""

    # 确保目录存在
    os.makedirs("subtitles", exist_ok=True)
    
    # 创建示例文件
    sample_file = "subtitles/sample_english.srt"
    with open(sample_file, "w", encoding="utf-8") as f:
        f.write(sample_content)
    
    print(f"✅ 创建示例英文字幕: {sample_file}")
    return sample_file

def test_with_sample():
    """使用示例文件测试翻译"""
    print("🎯 使用示例文件测试翻译功能")
    
    # 创建示例文件
    sample_file = create_sample_english_srt()
    
    try:
        from process_videos_fixed import translate_srt_to_chinese
        
        # 执行翻译
        chinese_srt = translate_srt_to_chinese(sample_file)
        
        if os.path.exists(chinese_srt):
            print(f"✅ 示例翻译成功: {chinese_srt}")
            
            # 显示对比结果
            print("\n📊 翻译对比:")
            
            with open(sample_file, "r", encoding="utf-8") as f:
                english_content = f.read()
            
            with open(chinese_srt, "r", encoding="utf-8") as f:
                chinese_content = f.read()
            
            english_blocks = english_content.strip().split("\n\n")
            chinese_blocks = chinese_content.strip().split("\n\n")
            
            for i, (eng_block, chi_block) in enumerate(zip(english_blocks, chinese_blocks), 1):
                eng_lines = eng_block.split("\n")
                chi_lines = chi_block.split("\n")
                
                if len(eng_lines) >= 3 and len(chi_lines) >= 3:
                    print(f"  {i}. {eng_lines[1]}")
                    print(f"     英文: {eng_lines[2]}")
                    print(f"     中文: {chi_lines[2]}")
                    print()
            
            return True
        else:
            print("❌ 示例翻译失败")
            return False
            
    except Exception as e:
        print(f"❌ 示例测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 OpenAI翻译功能测试")
    print("=" * 50)
    
    # 测试API连接
    api_ok = test_openai_api_connection()
    
    if not api_ok:
        print("\n💡 请确保:")
        print("1. 已安装openai库: pip install openai")
        print("2. 已设置API密钥: export OPENAI_API_KEY='your-key'")
        print("3. 网络连接正常")
        return
    
    print("\n" + "=" * 50)
    
    # 测试翻译功能
    if os.path.exists("subtitles/output_10s_english.srt"):
        print("🎯 使用现有字幕文件测试")
        translation_ok = test_openai_translation()
    else:
        print("🎯 使用示例文件测试")
        translation_ok = test_with_sample()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"  API连接: {'✅ 成功' if api_ok else '❌ 失败'}")
    print(f"  翻译功能: {'✅ 成功' if translation_ok else '❌ 失败'}")
    
    if api_ok and translation_ok:
        print("\n🎉 所有测试通过！OpenAI翻译功能工作正常")
        print("\n💡 使用方法:")
        print("  python process_videos_fixed.py --stage 2")
    else:
        print("\n⚠️  部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
