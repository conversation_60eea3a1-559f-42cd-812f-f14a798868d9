#!/usr/bin/env python3
"""
测试MPS加速功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_device_detection():
    """测试设备检测功能"""
    try:
        from process_videos_mps import detect_device, get_system_info
        
        print("🔍 测试设备检测功能")
        print("=" * 40)
        
        system_info = get_system_info()
        print(f"💻 系统信息: {system_info}")
        
        device = detect_device()
        print(f"🔧 检测到的设备: {device}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设备检测失败: {e}")
        return False

def test_whisper_model_loading():
    """测试Whisper模型加载"""
    try:
        from process_videos_mps import load_whisper_model, WHISPER_AVAILABLE
        
        print("\n🧪 测试Whisper模型加载")
        print("=" * 40)
        
        if not WHISPER_AVAILABLE:
            print("⚠️  Whisper未安装，跳过模型测试")
            return True
        
        # 尝试加载small模型进行快速测试
        print("📦 尝试加载small模型进行测试...")
        model, device = load_whisper_model("small")
        
        print(f"✅ 模型加载成功: {device}")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print("💡 这可能是由于MPS兼容性问题，脚本会自动回退到CPU模式")
        return False

def test_stage_4():
    """测试阶段4 - 视频合成"""
    try:
        from process_videos_mps import run_stage_4_merge_video
        
        print("\n🎥 测试阶段4 - 视频合成")
        print("=" * 40)
        
        # 检查是否有现有的ASS字幕文件
        import glob
        ass_files = glob.glob("subtitles/*_bilingual.ass")
        
        if ass_files:
            print(f"📁 找到ASS字幕文件: {len(ass_files)} 个")
            print("🔄 尝试运行阶段4...")
            run_stage_4_merge_video()
            print("✅ 阶段4测试完成")
        else:
            print("⚠️  没有找到ASS字幕文件，跳过阶段4测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 阶段4测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 MPS加速版视频处理工具测试")
    print("=" * 50)
    
    tests = [
        ("设备检测", test_device_detection),
        ("Whisper模型加载", test_whisper_model_loading),
        ("阶段4视频合成", test_stage_4),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MPS加速版本工作正常")
    else:
        print("⚠️  部分测试失败，但脚本应该仍能正常工作（会回退到兼容模式）")

if __name__ == "__main__":
    main()
