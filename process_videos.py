#!/usr/bin/env python3
"""
高质量双语视频字幕生成工具

功能特点:
- 使用Whisper large模型进行高质量语音转录
- 自动生成英文字幕和中文翻译
- 支持关键词高亮（不同优先级，不同颜色）
- 生成ASS格式字幕，支持丰富的样式
- 自动合成带字幕的最终视频

使用方法:
1. 将MP4视频文件放入 'videos' 目录
2. 运行脚本: python process_videos.py
3. 处理完成的视频将保存在 'output' 目录

依赖要求:
- whisper (pip install openai-whisper)
- ffmpeg (系统安装)

作者: AI Assistant
版本: 2.0 (改进版)
"""

import os
import re
import subprocess
from glob import glob

VIDEO_DIR = "videos"
SUBTITLE_DIR = "subtitles"
OUTPUT_DIR = "output"

# FFmpeg路径配置 - 优先使用Homebrew版本
FFMPEG_PATH = "/opt/homebrew/bin/ffmpeg"
if not os.path.exists(FFMPEG_PATH):
    FFMPEG_PATH = "ffmpeg"  # 回退到系统PATH中的ffmpeg

# 关键词配置 - 可以根据需要添加更多关键词
KEYWORDS = [
    "AI",
    "NVIDIA",
    "future",
    "students",
    "learning",
    "innovation",
    "technology",
    "artificial intelligence",
    "machine learning",
    "deep learning",
    "GPU",
    "computing",
    "data",
    "algorithm",
    "neural network",
    "robotics",
    "automation",
    "digital transformation",
]

# 关键词高亮样式配置
KEYWORD_STYLES = {
    "high_priority": r"{\\fs28\\c&HFF0000&\\b1}",  # 红色加粗
    "medium_priority": r"{\\fs26\\c&H00FF00&\\b1}",  # 绿色加粗
    "normal": r"{\\fs24\\c&HFFFFFF&\\b0}",  # 白色正常
}


def ensure_dirs():
    os.makedirs(SUBTITLE_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)


def apply_keyword_highlighting(text, keywords):
    """对文本应用关键词高亮，支持不同优先级的关键词"""
    highlighted_text = text

    # 高优先级关键词（红色加粗）
    high_priority_keywords = [
        "AI",
        "NVIDIA",
        "artificial intelligence",
        "machine learning",
        "deep learning",
    ]

    # 中优先级关键词（绿色加粗）
    medium_priority_keywords = [
        "future",
        "innovation",
        "technology",
        "GPU",
        "computing",
    ]

    # 应用高优先级关键词高亮
    for kw in high_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['high_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )

    # 应用中优先级关键词高亮
    for kw in medium_priority_keywords:
        if kw.lower() in [k.lower() for k in keywords]:
            highlighted_text = re.sub(
                rf"\b({re.escape(kw)})\b",
                rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
                highlighted_text,
                flags=re.IGNORECASE,
            )

    # 应用其他关键词的普通高亮
    other_keywords = [
        kw
        for kw in keywords
        if kw.lower()
        not in [k.lower() for k in high_priority_keywords + medium_priority_keywords]
    ]

    for kw in other_keywords:
        highlighted_text = re.sub(
            rf"\b({re.escape(kw)})\b",
            rf"{KEYWORD_STYLES['medium_priority']}\1{KEYWORD_STYLES['normal']}",
            highlighted_text,
            flags=re.IGNORECASE,
        )

    return highlighted_text


def transcribe_whisper_bilingual(video_path):
    """使用Whisper large模型生成英文字幕，然后生成中文翻译"""
    base_name = os.path.splitext(os.path.basename(video_path))[0]

    # 生成英文字幕
    print(f"🎯 生成英文字幕...")
    subprocess.run(
        [
            "whisper",
            video_path,
            "--model",
            "large",
            "--language",
            "English",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )

    # 生成中文翻译字幕
    print(f"🌏 生成中文翻译字幕...")
    subprocess.run(
        [
            "whisper",
            video_path,
            "--model",
            "large",
            "--task",
            "translate",
            "--language",
            "English",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )

    # 定义文件路径
    temp_srt = os.path.join(SUBTITLE_DIR, base_name + ".srt")
    english_srt = os.path.join(SUBTITLE_DIR, base_name + "_english.srt")
    chinese_srt = os.path.join(SUBTITLE_DIR, base_name + "_chinese.srt")

    # 将英文字幕重命名保存
    if os.path.exists(temp_srt):
        os.rename(temp_srt, english_srt)
        print(f"✅ 英文字幕已保存: {os.path.basename(english_srt)}")

    # 重新生成中文翻译字幕（因为上面的翻译命令会覆盖原文件）
    print(f"🌏 重新生成中文翻译字幕...")
    subprocess.run(
        [
            "whisper",
            video_path,
            "--model",
            "large",
            "--task",
            "translate",
            "--language",
            "English",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )

    # 将中文翻译重命名保存
    if os.path.exists(temp_srt):
        os.rename(temp_srt, chinese_srt)
        print(f"✅ 中文字幕已保存: {os.path.basename(chinese_srt)}")

    return english_srt, chinese_srt


def merge_bilingual_srt(english_srt, chinese_srt):
    """合并英文和中文字幕文件为双语字幕"""
    with open(english_srt, "r", encoding="utf-8") as f:
        english_blocks = f.read().strip().split("\n\n")

    with open(chinese_srt, "r", encoding="utf-8") as f:
        chinese_blocks = f.read().strip().split("\n\n")

    merged_blocks = []

    # 确保两个字幕文件有相同数量的块
    min_blocks = min(len(english_blocks), len(chinese_blocks))

    for i in range(min_blocks):
        eng_lines = english_blocks[i].strip().split("\n")
        chi_lines = chinese_blocks[i].strip().split("\n")

        if len(eng_lines) >= 3 and len(chi_lines) >= 3:
            # 使用英文字幕的时间码
            index = eng_lines[0]
            timecode = eng_lines[1]
            english_text = " ".join(eng_lines[2:])
            chinese_text = " ".join(chi_lines[2:])

            # 合并为双语字幕
            merged_block = f"{index}\n{timecode}\n{english_text}\n{chinese_text}\n"
            merged_blocks.append(merged_block)

    # 保存合并后的字幕
    base_name = os.path.splitext(english_srt)[0]
    merged_path = base_name + "_bilingual.srt"

    with open(merged_path, "w", encoding="utf-8") as f:
        f.write("\n".join(merged_blocks))

    return merged_path


def bilingual_srt_to_ass(bilingual_srt, ass_path, keywords):
    """将双语字幕转换为ASS格式，支持关键词高亮"""
    with open(bilingual_srt, "r", encoding="utf-8") as f:
        srt_data = f.read()

    dialogues = []
    for block in srt_data.strip().split("\n\n"):
        lines = block.split("\n")
        if len(lines) < 4:  # 双语字幕至少需要4行：序号、时间、英文、中文
            continue

        start, end = lines[1].split(" --> ")
        english_text = lines[2]
        chinese_text = lines[3]

        # 对英文字幕应用关键词高亮
        highlighted_english = apply_keyword_highlighting(english_text, keywords)

        # 时间格式转换
        start = start.replace(",", ".")
        end = end.replace(",", ".")

        # 创建英文字幕对话行（上方显示）
        english_dialogue = (
            f"Dialogue: 0,{start},{end},English,,0,0,0,,{highlighted_english}"
        )
        dialogues.append(english_dialogue)

        # 创建中文字幕对话行（下方显示）
        chinese_dialogue = f"Dialogue: 0,{start},{end},Chinese,,0,0,0,,{chinese_text}"
        dialogues.append(chinese_dialogue)

    # 改进的ASS样式头部，支持双语显示
    header = """[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: English,Arial,24,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,8,10,10,60,1
Style: Chinese,Microsoft YaHei,22,&H00FFFF00,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,20,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
    with open(ass_path, "w", encoding="utf-8") as f:
        f.write(header + "\n".join(dialogues))


def convert_ass_to_srt(ass_path, srt_path):
    """将ASS字幕转换为SRT格式（去除样式标签）"""
    try:
        with open(ass_path, "r", encoding="utf-8") as f:
            ass_content = f.read()

        # 提取对话行
        dialogue_lines = []
        for line in ass_content.split("\n"):
            if line.startswith("Dialogue:"):
                dialogue_lines.append(line)

        srt_blocks = []
        subtitle_index = 1

        for dialogue in dialogue_lines:
            # 解析ASS对话行格式
            # Dialogue: Layer,Start,End,Style,Name,MarginL,MarginR,MarginV,Effect,Text
            parts = dialogue.split(",", 9)
            if len(parts) >= 10:
                start_time = parts[1]
                end_time = parts[2]
                text = parts[9]

                # 清理ASS样式标签
                import re

                text = re.sub(r"\{[^}]*\}", "", text)  # 移除所有ASS样式标签
                text = text.strip()

                if text:  # 只有非空文本才添加
                    # 转换时间格式从ASS到SRT
                    start_srt = convert_ass_time_to_srt(start_time)
                    end_srt = convert_ass_time_to_srt(end_time)

                    srt_block = f"{subtitle_index}\n{start_srt} --> {end_srt}\n{text}\n"
                    srt_blocks.append(srt_block)
                    subtitle_index += 1

        # 写入SRT文件
        with open(srt_path, "w", encoding="utf-8") as f:
            f.write("\n".join(srt_blocks))

        print(f"✅ ASS转SRT完成: {os.path.basename(srt_path)}")

    except Exception as e:
        print(f"❌ ASS转SRT失败: {e}")
        raise


def convert_ass_time_to_srt(ass_time):
    """将ASS时间格式转换为SRT时间格式"""
    # ASS格式: H:MM:SS.CC (小时:分钟:秒.厘秒)
    # SRT格式: HH:MM:SS,mmm (小时:分钟:秒,毫秒)

    try:
        # 分割时间部分
        time_parts = ass_time.split(":")
        hours = int(time_parts[0])
        minutes = int(time_parts[1])
        seconds_and_centiseconds = time_parts[2].split(".")
        seconds = int(seconds_and_centiseconds[0])
        centiseconds = (
            int(seconds_and_centiseconds[1]) if len(seconds_and_centiseconds) > 1 else 0
        )

        # 转换为毫秒
        milliseconds = centiseconds * 10

        # 格式化为SRT时间
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

    except (ValueError, IndexError):
        # 如果转换失败，返回默认时间
        return "00:00:00,000"


def merge_video_with_ass(video_path, ass_path):
    """合成视频和字幕，支持多种方法"""
    filename = os.path.splitext(os.path.basename(video_path))[0]
    output_path = os.path.join(OUTPUT_DIR, filename + "_final.mp4")

    # 方法1: 尝试使用subtitles滤镜（需要libass支持）
    try:
        print(f"🎬 尝试使用subtitles滤镜合成视频...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",  # 覆盖输出文件
                "-i",
                video_path,
                "-vf",
                f"subtitles={ass_path}",
                "-c:a",
                "copy",
                output_path,
            ],
            capture_output=True,
            text=True,
            check=True,
        )
        print(f"✅ 合成完成: {output_path}")
        return
    except subprocess.CalledProcessError as e:
        print(f"⚠️  subtitles滤镜失败，尝试备用方法...")
        print(f"   错误信息: {e.stderr}")

    # 方法2: 使用SRT字幕作为备用方案
    try:
        # 生成SRT版本的字幕
        srt_path = ass_path.replace(".ass", ".srt")
        convert_ass_to_srt(ass_path, srt_path)

        print(f"🎬 使用SRT字幕合成视频...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",
                "-i",
                video_path,
                "-i",
                srt_path,
                "-c:v",
                "copy",
                "-c:a",
                "copy",
                "-c:s",
                "mov_text",
                "-metadata:s:s:0",
                "language=eng",
                output_path,
            ],
            check=True,
        )
        print(f"✅ 合成完成 (使用SRT字幕): {output_path}")
        return
    except subprocess.CalledProcessError as e:
        print(f"⚠️  SRT字幕方法也失败，使用基础合成...")

    # 方法3: 基础视频复制（无字幕烧录）
    try:
        print(f"🎬 创建无烧录字幕的视频副本...")
        subprocess.run(
            [
                FFMPEG_PATH,
                "-y",
                "-i",
                video_path,
                "-c",
                "copy",
                output_path,
            ],
            check=True,
        )
        print(f"✅ 视频复制完成: {output_path}")
        print(f"📝 字幕文件已保存: {ass_path}")
        print(f"💡 提示: 可以使用支持ASS字幕的播放器（如VLC）来播放带字幕的视频")
    except subprocess.CalledProcessError as e:
        print(f"❌ 所有方法都失败了: {e}")
        raise


def process_all_videos():
    """处理所有视频，生成高质量双语字幕"""
    ensure_dirs()

    # 获取所有视频文件
    video_files = glob(os.path.join(VIDEO_DIR, "*.mp4"))
    total_videos = len(video_files)

    if total_videos == 0:
        print(f"❌ 在 {VIDEO_DIR} 目录中没有找到MP4视频文件")
        return

    print(f"📁 找到 {total_videos} 个视频文件")
    print(f"🔧 使用Whisper large模型进行转录")
    print(f"🎯 关键词高亮: {len(KEYWORDS)} 个关键词")
    print("=" * 60)

    processed_count = 0
    failed_count = 0

    for i, video in enumerate(video_files, 1):
        try:
            print(f"\n🎬 [{i}/{total_videos}] 处理视频: {os.path.basename(video)}")

            # # 使用Whisper large模型生成双语字幕
            # english_srt, chinese_srt = transcribe_whisper_bilingual(video)

            # # 合并双语字幕
            # print(f"🔗 合并双语字幕...")
            # bilingual_srt = merge_bilingual_srt(english_srt, chinese_srt)

            # # 转换为ASS格式并应用关键词高亮
            # base = os.path.splitext(os.path.basename(video))[0]
            # ass_path = os.path.join(SUBTITLE_DIR, base + "_bilingual.ass")
            # print(f"✨ 生成ASS字幕文件...")
            # bilingual_srt_to_ass(bilingual_srt, ass_path, KEYWORDS)

            # 合成最终视频
            print(f"🎥 合成最终视频...")
            merge_video_with_ass(video, ass_path)

            processed_count += 1
            print(f"✅ 视频处理完成: {os.path.basename(video)}")

        except Exception as e:
            failed_count += 1
            print(f"❌ 处理视频失败: {os.path.basename(video)}")
            print(f"   错误信息: {str(e)}")

    # 显示处理统计
    print("\n" + "=" * 60)
    print(f"📊 处理完成统计:")
    print(f"   ✅ 成功处理: {processed_count} 个视频")
    print(f"   ❌ 处理失败: {failed_count} 个视频")
    print(f"   📁 输出目录: {OUTPUT_DIR}")
    print("=" * 60)


if __name__ == "__main__":
    process_all_videos()
