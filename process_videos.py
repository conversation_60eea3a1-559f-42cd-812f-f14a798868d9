import os
import re
import subprocess
from glob import glob
from translate import Translator  # pip install translate

VIDEO_DIR = "videos"
SUBTITLE_DIR = "subtitles"
OUTPUT_DIR = "output"
KEYWORDS = ["AI", "NVIDIA", "future", "students", "learning", "innovation"]


def ensure_dirs():
    os.makedirs(SUBTITLE_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)


def transcribe_whisper(video_path):
    subprocess.run(
        [
            "whisper",
            video_path,
            "--language",
            "English",
            "--output_format",
            "srt",
            "--output_dir",
            SUBTITLE_DIR,
        ]
    )


def translate_srt(srt_path):
    translator = Translator(to_lang="zh")
    output_lines = []
    with open(srt_path, "r", encoding="utf-8") as f:
        blocks = f.read().split("\n\n")

    for block in blocks:
        lines = block.strip().split("\n")
        if len(lines) >= 3:
            index, timecode, *text_lines = lines
            text = " ".join(text_lines)
            try:
                chinese = translator.translate(text)
            except:
                chinese = "[翻译失败]"
            merged = f"{index}\n{timecode}\n{text}\n{chinese}\n"
            output_lines.append(merged)

    translated_path = srt_path.replace(".srt", "_translated.srt")
    with open(translated_path, "w", encoding="utf-8") as f:
        f.write("\n".join(output_lines))
    return translated_path


def srt_to_ass(translated_srt, ass_path, keywords):
    with open(translated_srt, "r", encoding="utf-8") as f:
        srt_data = f.read()

    dialogues = []
    for block in srt_data.strip().split("\n\n"):
        lines = block.split("\n")
        if len(lines) < 3:
            continue
        index = lines[0]
        start, end = lines[1].split(" --> ")
        content = lines[2:]
        text = " ".join(content)

        for kw in keywords:
            text = re.sub(
                rf"\b({kw})\b",
                r"{\\fs36\\c&HFF0000&}\1{\\fs24\\c&HFFFFFF&}",
                text,
                flags=re.IGNORECASE,
            )

        start = start.replace(",", ".")
        end = end.replace(",", ".")
        dialogue = f"Dialogue: 0,{start},{end},Default,,0,0,0,,{text}"
        dialogues.append(dialogue)

    header = """[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, Bold, Italic, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,24,&H00FFFFFF,0,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
    with open(ass_path, "w", encoding="utf-8") as f:
        f.write(header + "\n".join(dialogues))


def merge_video_with_ass(video_path, ass_path):
    filename = os.path.splitext(os.path.basename(video_path))[0]
    output_path = os.path.join(OUTPUT_DIR, filename + "_final.mp4")
    subprocess.run(
        [
            "ffmpeg",
            "-i",
            video_path,
            "-vf",
            f"subtitles={ass_path}",
            "-c:a",
            "copy",
            output_path,
        ]
    )
    print(f"✅ 合成完成: {output_path}")


def process_all_videos():
    ensure_dirs()
    for video in glob(os.path.join(VIDEO_DIR, "*.mp4")):
        print(f"\n🎬 处理视频: {video}")
        transcribe_whisper(video)
        base = os.path.splitext(os.path.basename(video))[0]
        srt_path = os.path.join(SUBTITLE_DIR, base + ".srt")
        translated_srt = translate_srt(srt_path)
        ass_path = os.path.join(SUBTITLE_DIR, base + ".ass")
        srt_to_ass(translated_srt, ass_path, KEYWORDS)
        merge_video_with_ass(video, ass_path)


if __name__ == "__main__":
    process_all_videos()
