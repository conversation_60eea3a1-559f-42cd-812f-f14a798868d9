# 高质量双语视频字幕生成工具 v2.0

## 🚀 功能特点

### ✨ 核心改进
- **使用Whisper large模型**：更高质量的语音转录
- **直接生成中英文字幕**：避免额外翻译步骤，提高准确性
- **智能关键词高亮**：支持不同优先级的关键词，不同颜色显示
- **优化的ASS字幕格式**：更好的视觉效果和字幕布局
- **完整的错误处理**：处理过程更稳定可靠

### 🎯 关键词高亮系统
- **高优先级关键词**（红色加粗）：AI, NVIDIA, artificial intelligence, machine learning, deep learning
- **中优先级关键词**（绿色加粗）：future, innovation, technology, GPU, computing
- **其他关键词**（绿色标准）：students, learning, data, algorithm, neural network, robotics, automation, digital transformation

## 📋 使用方法

### 1. 环境准备
```bash
# 安装Whisper
pip install openai-whisper

# 确保系统已安装ffmpeg
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
# Windows: 下载ffmpeg并添加到PATH
```

### 2. 目录结构
```
project/
├── process_videos.py    # 主脚本
├── videos/             # 放置MP4视频文件
├── subtitles/          # 生成的字幕文件（自动创建）
└── output/             # 最终输出视频（自动创建）
```

### 3. 运行脚本
```bash
# 将MP4视频文件放入videos目录
# 然后运行脚本
python process_videos.py
```

## 🔧 配置选项

### 关键词自定义
在脚本中修改 `KEYWORDS` 列表来添加或删除关键词：
```python
KEYWORDS = [
    "AI", "NVIDIA", "future", "students", "learning", "innovation",
    # 添加你的关键词...
]
```

### 样式自定义
修改 `KEYWORD_STYLES` 来调整高亮样式：
```python
KEYWORD_STYLES = {
    "high_priority": r"{\\fs28\\c&HFF0000&\\b1}",  # 红色加粗
    "medium_priority": r"{\\fs26\\c&H00FF00&\\b1}",  # 绿色加粗
    "normal": r"{\\fs24\\c&HFFFFFF&\\b0}"  # 白色正常
}
```

## 📊 输出文件

### 字幕文件（subtitles目录）
- `{video_name}_english.srt` - 英文字幕
- `{video_name}_chinese.srt` - 中文翻译字幕
- `{video_name}_bilingual.srt` - 合并的双语字幕
- `{video_name}_bilingual.ass` - ASS格式字幕（带高亮）

### 最终视频（output目录）
- `{video_name}_final.mp4` - 带双语字幕的最终视频

## 🎨 字幕样式说明

### 英文字幕
- 位置：视频上方
- 字体：Arial, 24px
- 颜色：白色
- 关键词高亮：根据优先级显示不同颜色

### 中文字幕
- 位置：视频下方
- 字体：Microsoft YaHei, 22px
- 颜色：黄色
- 无关键词高亮

## ⚡ 性能优化

### Whisper模型选择
- **large模型**：最高质量，处理速度较慢
- 如需更快处理，可修改为 `medium` 或 `base` 模型

### 批量处理
- 脚本自动处理videos目录中的所有MP4文件
- 支持错误恢复，单个文件失败不影响其他文件处理

## 🐛 常见问题

### 1. Whisper模型下载慢
```bash
# 预先下载large模型
whisper --model large dummy.wav
```

### 2. ffmpeg未找到
确保ffmpeg已正确安装并添加到系统PATH

### 3. 内存不足
如果处理大文件时内存不足，可以：
- 使用smaller模型（medium, base, small）
- 分批处理视频文件

## 📈 版本历史

### v2.0 (当前版本)
- ✅ 升级到Whisper large模型
- ✅ 直接生成双语字幕
- ✅ 智能关键词高亮系统
- ✅ 改进的ASS字幕样式
- ✅ 完整的错误处理和进度显示

### v1.0 (原版本)
- 基础Whisper转录
- 外部翻译API
- 简单关键词高亮
- 基础ASS格式

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License
