# 分阶段运行指南

## 🎯 概述

现在您可以分阶段运行视频处理流程，每个阶段都可以独立执行：

- **阶段1**: Whisper转录生成双语字幕
- **阶段2**: 合并英文和中文字幕
- **阶段3**: 生成ASS格式字幕（带关键词高亮）
- **阶段4**: 合成最终视频

## 📋 命令行用法

### 查看帮助
```bash
python process_videos.py --help
```

### 运行完整流程（默认）
```bash
python process_videos.py
# 或
python process_videos.py --all
```

### 分阶段运行

#### 阶段1: 转录字幕
```bash
# 处理所有视频
python process_videos.py --stage 1

# 处理指定视频
python process_videos.py --stage 1 --video videos/output_last_2min.mp4
```

**输出文件:**
- `subtitles/{video_name}_english.srt` - 英文字幕
- `subtitles/{video_name}_chinese.srt` - 中文字幕

#### 阶段2: 合并字幕
```bash
# 合并所有可用的字幕文件
python process_videos.py --stage 2

# 合并指定视频的字幕
python process_videos.py --stage 2 --video videos/output_last_2min.mp4
```

**输出文件:**
- `subtitles/{video_name}_english_bilingual.srt` - 双语字幕

#### 阶段3: 生成ASS字幕
```bash
# 为所有双语字幕生成ASS格式
python process_videos.py --stage 3

# 为指定视频生成ASS字幕
python process_videos.py --stage 3 --video videos/output_last_2min.mp4
```

**输出文件:**
- `subtitles/{video_name}_bilingual.ass` - ASS格式字幕（带关键词高亮）

#### 阶段4: 合成视频
```bash
# 合成所有可用的视频
python process_videos.py --stage 4

# 合成指定视频
python process_videos.py --stage 4 --video videos/output_last_2min.mp4
```

**输出文件:**
- `output/{video_name}_final.mp4` - 带字幕的最终视频

## 🔄 典型工作流程

### 场景1: 完整处理
```bash
python process_videos.py
```

### 场景2: 分步处理（调试/优化）
```bash
# 步骤1: 先转录字幕
python process_videos.py --stage 1

# 步骤2: 检查字幕质量，然后合并
python process_videos.py --stage 2

# 步骤3: 生成ASS字幕（可以修改关键词后重新运行）
python process_videos.py --stage 3

# 步骤4: 最终合成视频
python process_videos.py --stage 4
```

### 场景3: 重新生成ASS字幕（修改关键词后）
```bash
# 修改 process_videos.py 中的 KEYWORDS 列表
# 然后重新生成ASS字幕
python process_videos.py --stage 3

# 重新合成视频
python process_videos.py --stage 4
```

### 场景4: 处理单个视频
```bash
# 完整处理单个视频
python process_videos.py --video videos/my_video.mp4

# 或分阶段处理
python process_videos.py --stage 1 --video videos/my_video.mp4
python process_videos.py --stage 2 --video videos/my_video.mp4
python process_videos.py --stage 3 --video videos/my_video.mp4
python process_videos.py --stage 4 --video videos/my_video.mp4
```

## 📁 文件结构示例

```
project/
├── videos/
│   └── output_last_2min.mp4
├── subtitles/
│   ├── output_last_2min_english.srt      # 阶段1输出
│   ├── output_last_2min_chinese.srt      # 阶段1输出
│   ├── output_last_2min_english_bilingual.srt  # 阶段2输出
│   └── output_last_2min_bilingual.ass    # 阶段3输出
├── output/
│   └── output_last_2min_final.mp4        # 阶段4输出
└── process_videos.py
```

## 🛠️ 故障排除

### 阶段1失败
- 检查Whisper是否正确安装: `whisper --help`
- 检查视频文件是否存在且格式正确

### 阶段2失败
- 确保阶段1已完成，存在对应的英文和中文字幕文件
- 检查字幕文件格式是否正确

### 阶段3失败
- 确保阶段2已完成，存在双语字幕文件
- 检查关键词列表是否正确

### 阶段4失败
- 确保阶段3已完成，存在ASS字幕文件
- 检查FFmpeg是否支持字幕滤镜: `python ffmpeg_diagnosis.py`

## 💡 优化建议

1. **批量处理**: 不指定`--video`参数可以批量处理所有视频
2. **增量处理**: 只有当前阶段的输入文件存在时，该阶段才会执行
3. **重复执行**: 可以安全地重复执行任何阶段，会覆盖之前的输出
4. **自定义关键词**: 修改代码中的`KEYWORDS`列表后，重新运行阶段3和4

## 🎨 自定义选项

### 修改关键词
编辑 `process_videos.py` 中的 `KEYWORDS` 列表：
```python
KEYWORDS = [
    "AI", "NVIDIA", "future", "students", "learning", "innovation",
    # 添加您的关键词...
]
```

### 修改Whisper模型
在 `transcribe_whisper_bilingual` 函数中修改：
```python
"--model", "large",  # 改为 "medium", "base", "small"
```

### 修改字幕样式
在 `KEYWORD_STYLES` 字典中修改ASS样式代码。
