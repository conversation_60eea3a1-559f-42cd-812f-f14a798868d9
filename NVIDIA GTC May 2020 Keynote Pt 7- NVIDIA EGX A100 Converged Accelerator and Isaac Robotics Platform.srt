1
00:00:03,620 --> 00:00:05,969
one of the most exciting opportunities

2
00:00:06,480 --> 00:00:12,960
in computing is what's called edge AI

3
00:00:09,589 --> 00:00:16,409
this is where IOT and AI comes together

4
00:00:12,960 --> 00:00:18,139
to revolutionize devices trillions of

5
00:00:16,410 --> 00:00:20,969
devices will be all of the world

6
00:00:18,140 --> 00:00:24,029
embedded with sensors connected to the

7
00:00:20,969 --> 00:00:27,268
Internet data centers running algorithms

8
00:00:24,028 --> 00:00:29,518
that infuse them with intelligence this

9
00:00:27,268 --> 00:00:32,069
is going to be the smart everything

10
00:00:29,518 --> 00:00:34,558
revolution just as the phone became the

11
00:00:32,070 --> 00:00:36,959
smartphone and revolutionized the

12
00:00:34,558 --> 00:00:39,058
industry of communications and created

13
00:00:36,960 --> 00:00:40,559
large industries around it the same

14
00:00:39,058 --> 00:00:43,109
thing is gonna happen to devices and

15
00:00:40,558 --> 00:00:44,909
things devices with sensors are going to

16
00:00:43,109 --> 00:00:46,528
be connected to the Internet data

17
00:00:44,909 --> 00:00:49,498
centers are going to have amazing

18
00:00:46,530 --> 00:00:52,589
algorithms that infuse these devices

19
00:00:49,500 --> 00:00:53,910
with apparent intelligence these devices

20
00:00:52,590 --> 00:00:55,980
are going to be everywhere whereas there

21
00:00:53,909 --> 00:00:58,319
were only billions of smart phones there

22
00:00:55,979 --> 00:00:59,398
will be trillions of things and then

23
00:00:58,320 --> 00:01:01,469
will be in all these different

24
00:00:59,399 --> 00:01:05,599
industries agriculture manufacturing

25
00:01:01,469 --> 00:01:09,510
logistics retail stores warehouses

26
00:01:05,599 --> 00:01:11,818
airports train stations streets we will

27
00:01:09,510 --> 00:01:13,409
have sensors all over the world and

28
00:01:11,819 --> 00:01:15,059
there will be intelligence applied to

29
00:01:13,409 --> 00:01:18,540
them the fundamental difference between

30
00:01:15,060 --> 00:01:21,329
a smartphone and these Internet of

31
00:01:18,540 --> 00:01:23,129
Things is the continuation of the sensor

32
00:01:21,329 --> 00:01:25,079
information that would be coming whereas

33
00:01:23,129 --> 00:01:27,839
most users interact with their phones

34
00:01:25,079 --> 00:01:30,269
every now and then an electronic time

35
00:01:27,840 --> 00:01:33,630
the time separation between clicks is

36
00:01:30,269 --> 00:01:36,749
basically infinite these sensors are on

37
00:01:33,629 --> 00:01:38,428
all the time they're monitoring picking

38
00:01:36,750 --> 00:01:41,309
up data reasoning about what they're

39
00:01:38,430 --> 00:01:43,500
sensing and taking necessary action it's

40
00:01:41,310 --> 00:01:45,390
important that the data center is placed

41
00:01:43,500 --> 00:01:47,399
close to the point of action where the

42
00:01:45,390 --> 00:01:49,529
data is being collected otherwise the

43
00:01:47,399 --> 00:01:51,719
cost of streaming all of this enormous

44
00:01:49,530 --> 00:01:53,640
amount of data onto the Internet is

45
00:01:51,719 --> 00:01:55,169
going to be cost prohibitive it's also

46
00:01:53,640 --> 00:01:57,479
important to put the data center close

47
00:01:55,170 --> 00:01:59,309
by so that you could sense and react to

48
00:01:57,478 --> 00:02:01,319
the environment as quickly as possible

49
00:01:59,310 --> 00:02:03,478
we're a few millisecond makes a

50
00:02:01,319 --> 00:02:05,397
difference the speed of light traveling

51
00:02:03,478 --> 00:02:08,367
to data centers far away takes too long

52
00:02:05,399 --> 00:02:10,799
and finally in many industries there are

53
00:02:08,368 --> 00:02:12,869
data privacy and data sovereignty issues

54
00:02:10,800 --> 00:02:13,949
in the future there will be millions of

55
00:02:12,870 --> 00:02:15,660
data centers

56
00:02:13,949 --> 00:02:17,339
over the world close to the point of

57
00:02:15,659 --> 00:02:20,237
action close to where the data is

58
00:02:17,340 --> 00:02:22,590
generated and sensed and processing AI

59
00:02:20,239 --> 00:02:24,090
instantaneously these data centers are

60
00:02:22,590 --> 00:02:25,619
going to be very much like the cloud

61
00:02:24,090 --> 00:02:29,009
data centers today their cloud native

62
00:02:25,620 --> 00:02:31,679
powerful and most importantly secure we

63
00:02:29,009 --> 00:02:32,669
created a computing platform for this

64
00:02:31,680 --> 00:02:35,340
edge AI

65
00:02:32,669 --> 00:02:38,728
application we call it the Nvidia ejects

66
00:02:35,340 --> 00:02:41,399
egx is made possible by two advanced

67
00:02:38,729 --> 00:02:43,679
processor the first is the ampère GPU

68
00:02:41,400 --> 00:02:45,629
which has been designed for high speed

69
00:02:43,680 --> 00:02:48,390
AI processing but there are other

70
00:02:45,628 --> 00:02:51,059
capabilities that ampere provides the

71
00:02:48,389 --> 00:02:53,238
first is secure and authenticated boot

72
00:02:51,060 --> 00:02:55,979
so that you know that this computer is

73
00:02:53,239 --> 00:02:58,439
authorized to be on the network and run

74
00:02:55,979 --> 00:03:01,708
the applications the second is a new

75
00:02:58,439 --> 00:03:04,738
security engine for confidential a I the

76
00:03:01,709 --> 00:03:07,680
AI algorithms are sensing reasoning and

77
00:03:04,739 --> 00:03:09,420
taking action so it's vitally important

78
00:03:07,680 --> 00:03:13,500
that we know it hasn't been tampered

79
00:03:09,419 --> 00:03:15,808
this secure confidential AI uncle' is

80
00:03:13,500 --> 00:03:18,120
going to protect the AI model so that

81
00:03:15,810 --> 00:03:19,920
it's encrypted and if it's tampered in

82
00:03:18,120 --> 00:03:21,959
any way the program would not be allowed

83
00:03:19,919 --> 00:03:25,438
to run the second processor is the

84
00:03:21,959 --> 00:03:27,779
nvidia Mellanox connect x6 DX it's a

85
00:03:25,439 --> 00:03:30,059
dual 100 gigabit per second Ethernet or

86
00:03:27,780 --> 00:03:33,180
InfiniBand it has a crypto engine to

87
00:03:30,060 --> 00:03:38,458
process security protocols TLS and IPSec

88
00:03:33,180 --> 00:03:40,888
at line speed it has a SEP 2 and packet

89
00:03:38,459 --> 00:03:43,798
processing with single route IO

90
00:03:40,889 --> 00:03:46,230
virtualization that allows this computer

91
00:03:43,799 --> 00:03:48,329
to be secure and virtualized and then

92
00:03:46,229 --> 00:03:50,818
lastly it has a brand new capability

93
00:03:48,329 --> 00:03:53,579
called time trigger transmission which

94
00:03:50,818 --> 00:03:56,308
connects the EGS server with the 5g

95
00:03:53,579 --> 00:03:57,989
radio antenna and synchronizes the

96
00:03:56,310 --> 00:04:00,149
transmission between the two of them the

97
00:03:57,989 --> 00:04:01,739
combination of these two processor makes

98
00:04:00,150 --> 00:04:03,779
up the nvidia egx

99
00:04:01,739 --> 00:04:07,130
and by installing the egx into a

100
00:04:03,780 --> 00:04:12,869
standard x86 server you turn it into a

101
00:04:07,129 --> 00:04:14,068
hyper-converged secure cloud native ai

102
00:04:12,870 --> 00:04:16,978
powerhouse

103
00:04:14,068 --> 00:04:21,537
it's basically an entire cloud

104
00:04:16,978 --> 00:04:24,239
datacenter in one box the nvidia egx

105
00:04:21,538 --> 00:04:26,129
card is the starting point what makes it

106
00:04:24,240 --> 00:04:27,749
amazing it's the software stack on top

107
00:04:26,129 --> 00:04:30,808
we call it the

108
00:04:27,750 --> 00:04:32,700
stack the magic of Nvidia egx comes

109
00:04:30,810 --> 00:04:35,970
alive because of the software stack

110
00:04:32,699 --> 00:04:39,029
fully integrated fully optimized it has

111
00:04:35,970 --> 00:04:40,590
four major pillars the first is that

112
00:04:39,029 --> 00:04:43,048
it's cloud native designed for

113
00:04:40,589 --> 00:04:45,959
kubernetes orchestrating containers

114
00:04:43,050 --> 00:04:48,030
managed from afar the ability to update

115
00:04:45,959 --> 00:04:51,388
software without rebooting the computer

116
00:04:48,029 --> 00:04:55,019
second it's the world's first GPU

117
00:04:51,389 --> 00:04:56,639
accelerated 5g baseband radio our first

118
00:04:55,019 --> 00:04:59,459
partner is Ericsson who is already

119
00:04:56,639 --> 00:05:02,759
developing 5g stack on top of Nvidia

120
00:04:59,459 --> 00:05:05,399
third a fully optimized high performance

121
00:05:02,759 --> 00:05:08,249
AI processing pipeline that we are world

122
00:05:05,399 --> 00:05:10,559
famous for and then lastly this entire

123
00:05:08,250 --> 00:05:13,530
stack is optimized for networking

124
00:05:10,560 --> 00:05:16,619
storage and most importantly security

125
00:05:13,529 --> 00:05:19,738
this one box is essentially a

126
00:05:16,620 --> 00:05:22,259
state-of-the-art cloud native data

127
00:05:19,740 --> 00:05:25,199
center that is secure could be managed

128
00:05:22,259 --> 00:05:28,228
from afar and is tamper proof data is

129
00:05:25,199 --> 00:05:29,998
protected in motion and in place it is

130
00:05:28,230 --> 00:05:32,520
authenticated before it can come onto

131
00:05:30,000 --> 00:05:35,490
your network and as a result you could

132
00:05:32,519 --> 00:05:37,318
manage a fleet of data centers and all

133
00:05:35,490 --> 00:05:39,959
of those data centers are sprawled out

134
00:05:37,319 --> 00:05:43,708
all over your geography and all of your

135
00:05:39,959 --> 00:05:46,319
market connected the sensors applying

136
00:05:43,709 --> 00:05:48,779
intelligence to the products and

137
00:05:46,319 --> 00:05:50,668
services that you offer the egx stack

138
00:05:48,779 --> 00:05:52,769
comes with several reference

139
00:05:50,668 --> 00:05:54,449
applications the 5g reference

140
00:05:52,769 --> 00:05:56,478
application is one another reference

141
00:05:54,449 --> 00:05:59,218
application is called metropolis

142
00:05:56,478 --> 00:06:01,739
metropolis is designed for connecting

143
00:05:59,220 --> 00:06:04,019
multiple high-speed cameras or

144
00:06:01,740 --> 00:06:06,749
high-speed sensors to process the

145
00:06:04,019 --> 00:06:07,528
streaming data and do AI processing on

146
00:06:06,750 --> 00:06:09,859
top of that

147
00:06:07,529 --> 00:06:12,088
metropolis also comes with a library of

148
00:06:09,860 --> 00:06:13,560
pre-trained state of the art models

149
00:06:12,089 --> 00:06:15,418
these pre-trimmed models has been

150
00:06:13,560 --> 00:06:18,090
trained with a great deal of data we

151
00:06:15,418 --> 00:06:21,509
also provide transfer learning tools to

152
00:06:18,089 --> 00:06:23,909
adapt these models to your use case this

153
00:06:21,509 --> 00:06:28,018
end-to-end system is what we call Nvidia

154
00:06:23,910 --> 00:06:31,140
egx this is a demonstration of live

155
00:06:28,019 --> 00:06:35,608
cameras feeding multiple streams of

156
00:06:31,139 --> 00:06:39,597
video which then goes into a 5g radio

157
00:06:35,610 --> 00:06:41,610
emulator turns the live video into 5g

158
00:06:39,598 --> 00:06:44,697
packets the 5g

159
00:06:41,610 --> 00:06:49,068
Hackett's comes into the egx stack which

160
00:06:44,699 --> 00:06:52,109
runs the Phi G aerial SDK GPU software

161
00:06:49,069 --> 00:06:54,719
baseband Radio Arriola metropolis is

162
00:06:52,110 --> 00:06:57,719
running on top of the e GX stack which

163
00:06:54,718 --> 00:06:59,847
is running kubernetes CUDA AI processing

164
00:06:57,718 --> 00:07:02,038
network processing security processing

165
00:06:59,848 --> 00:07:04,828
and storage processing all completely

166
00:07:02,038 --> 00:07:06,627
optimized running on one computer and to

167
00:07:04,829 --> 00:07:09,688
end let me show you another application

168
00:07:06,629 --> 00:07:12,029
of Nvidia egx this is factory automation

169
00:07:09,689 --> 00:07:13,770
the reference application we developed

170
00:07:12,029 --> 00:07:16,407
for this is called Isaac

171
00:07:13,769 --> 00:07:18,687
it starts with Isaac's in a virtual

172
00:07:16,408 --> 00:07:21,089
reality environment that obeys the laws

173
00:07:18,689 --> 00:07:24,028
of physics appear photorealistic

174
00:07:21,089 --> 00:07:25,858
in that world a robot thinks it's in the

175
00:07:24,028 --> 00:07:28,407
physical real world we're gonna teach it

176
00:07:25,860 --> 00:07:30,778
skills and refine its skills once the

177
00:07:28,408 --> 00:07:34,438
robotics models develop we would run it

178
00:07:30,778 --> 00:07:37,138
on the nvidia egx computer running the

179
00:07:34,439 --> 00:07:37,528
Isaac robotics stack the Isaac robotics

180
00:07:37,139 --> 00:07:39,598
deck

181
00:07:37,528 --> 00:07:41,278
includes sensing models localization

182
00:07:39,598 --> 00:07:41,727
articulation models and navigation

183
00:07:41,278 --> 00:07:44,637
models

184
00:07:41,728 --> 00:07:47,848
it receives sensor information streamed

185
00:07:44,639 --> 00:07:49,860
over 5g from the fleet of robots it does

186
00:07:47,848 --> 00:07:51,868
the robotics processing sensing

187
00:07:49,860 --> 00:07:55,289
reasoning in action and sends the

188
00:07:51,870 --> 00:07:57,240
actuation commands back over 5g to the

189
00:07:55,288 --> 00:07:58,768
fleet of robots with extremely low

190
00:07:57,240 --> 00:08:03,028
latency which is one of the features of

191
00:07:58,769 --> 00:08:04,378
the 5g protocol Isaac is n2m from the

192
00:08:03,028 --> 00:08:06,959
virtual reality environment we call

193
00:08:04,379 --> 00:08:10,409
Isaac sim the robotic stack we called

194
00:08:06,959 --> 00:08:13,917
the Isaac SDK the computer and video EEG

195
00:08:10,408 --> 00:08:17,098
X and the environment for 5g and the

196
00:08:13,918 --> 00:08:20,637
robotic stack on top controlling a fleet

197
00:08:17,098 --> 00:08:23,198
of robots to automate factories of the

198
00:08:20,639 --> 00:08:57,590
future let me show to you

199
00:08:23,199 --> 00:08:57,589
[Music]

200
00:09:26,100 --> 00:09:30,609
this is have incredible ladies and

201
00:09:28,779 --> 00:09:33,368
gentlemen today I have really exciting

202
00:09:30,610 --> 00:09:35,499
news we've been working together with

203
00:09:33,370 --> 00:09:36,760
one of the world's leaders in building

204
00:09:35,500 --> 00:09:40,030
amazing machines

205
00:09:36,759 --> 00:09:42,339
ladies and gentlemen BMW BMW has chosen

206
00:09:40,029 --> 00:09:42,908
a video to build the factories of the

207
00:09:42,340 --> 00:09:44,949
future

208
00:09:42,909 --> 00:09:46,808
BMW manufactures some of the most

209
00:09:44,950 --> 00:09:49,419
amazing machines in the world and they

210
00:09:46,809 --> 00:09:51,639
do it in volume but what you don't

211
00:09:49,419 --> 00:09:53,318
realize each one of these cars they have

212
00:09:51,639 --> 00:09:56,318
40 different models have a hundred

213
00:09:53,320 --> 00:09:58,900
options every single day 30 million raw

214
00:09:56,320 --> 00:10:01,480
parts comes in from nearly 2,000

215
00:09:58,899 --> 00:10:03,339
suppliers it goes to 30 factories around

216
00:10:01,480 --> 00:10:07,359
the world and those 30 factories

217
00:10:03,340 --> 00:10:10,450
assemble one car every 56 seconds these

218
00:10:07,360 --> 00:10:13,060
30 million parts comes in sent over to

219
00:10:10,450 --> 00:10:15,640
the workspace just in time for the

220
00:10:13,059 --> 00:10:18,188
craftsmen to assemble those parts into

221
00:10:15,639 --> 00:10:21,308
the car the empty crates taken away a

222
00:10:18,190 --> 00:10:24,579
new crate arrives each and every step of

223
00:10:21,309 --> 00:10:28,599
the way a robot will be involved the

224
00:10:24,580 --> 00:10:31,330
splitting the picking the placing the

225
00:10:28,600 --> 00:10:33,939
delivery the picking up of the empties

226
00:10:31,330 --> 00:10:35,499
this is really a logistics miracle and

227
00:10:33,940 --> 00:10:37,779
it's one of the great challenges of

228
00:10:35,500 --> 00:10:40,269
automated Factory today and this is the

229
00:10:37,779 --> 00:10:42,548
future you're gonna have a factory

230
00:10:40,269 --> 00:10:45,549
that's gonna be designed as a robot mass

231
00:10:42,549 --> 00:10:46,837
production customization going

232
00:10:45,549 --> 00:10:48,609
hand-in-hand and what makes it possible

233
00:10:46,839 --> 00:10:51,159
is artificial intelligence and robotics

234
00:10:48,610 --> 00:10:54,010
I can't be more delighted to work with

235
00:10:51,159 --> 00:10:56,378
all of the great people at BMW in this

236
00:10:54,009 --> 00:10:59,138
great challenge to invent the future of

237
00:10:56,379 --> 00:11:01,058
automated factories as I mentioned the

238
00:10:59,139 --> 00:11:02,889
fusion of IOT and artificial

239
00:11:01,059 --> 00:11:04,958
intelligence is going to create this

240
00:11:02,889 --> 00:11:07,658
whole new computing space we call edge

241
00:11:04,960 --> 00:11:09,610
AI and the applications for it will be

242
00:11:07,659 --> 00:11:11,587
in so many industries we've already

243
00:11:09,610 --> 00:11:14,260
announced previously that we're working

244
00:11:11,589 --> 00:11:16,239
with Walmart on automated retail we've

245
00:11:14,259 --> 00:11:19,959
also talked about how we're working with

246
00:11:16,240 --> 00:11:22,299
USPS the world's highest volume highest

247
00:11:19,960 --> 00:11:23,379
speed sorting system is now powered by

248
00:11:22,299 --> 00:11:25,838
Nvidia egx

249
00:11:23,379 --> 00:11:30,518
today I announced our partnership with

250
00:11:25,839 --> 00:11:33,670
BMW to apply egx IOT AI and robotics

251
00:11:30,519 --> 00:11:36,368
technology to reinvent the future of

252
00:11:33,669 --> 00:11:38,609
factories while these industries are

253
00:11:36,370 --> 00:11:40,039
large the number of sensors

254
00:11:38,610 --> 00:11:42,239
the world is going to be enormous

255
00:11:40,039 --> 00:11:44,939
trillions of them collecting and

256
00:11:42,240 --> 00:11:46,740
processing data continuously vast

257
00:11:44,940 --> 00:11:49,829
majority the world data will be created

258
00:11:46,740 --> 00:11:53,850
and they are processing will be done the

259
00:11:49,828 --> 00:11:55,888
nvidia EG x stack has so many partners

260
00:11:53,850 --> 00:11:57,869
from operating systems security

261
00:11:55,889 --> 00:11:59,819
processing network processing and one of

262
00:11:57,870 --> 00:12:02,519
the most important is the 5g stack and

263
00:11:59,820 --> 00:12:04,860
our partnership with ericsson and mavin

264
00:12:02,519 --> 00:12:06,749
ear and in each one of the vertical

265
00:12:04,860 --> 00:12:08,909
industries there are great partners were

266
00:12:06,750 --> 00:12:11,250
working with to develop applications of

267
00:12:08,909 --> 00:12:14,368
specific types of AI and specific skills

268
00:12:11,250 --> 00:12:17,099
from industrial to medical to robotics

269
00:12:14,370 --> 00:12:18,749
to intelligent video analytics this is

270
00:12:17,100 --> 00:12:20,669
one of the most exciting computing

271
00:12:18,750 --> 00:12:23,219
platforms we've built bringing together

272
00:12:20,669 --> 00:12:25,678
in videos great ai processing and

273
00:12:23,220 --> 00:12:27,930
Mellanox is great network storage and

274
00:12:25,679 --> 00:12:31,409
security processing we've created a

275
00:12:27,929 --> 00:12:34,009
computing platform that has the power of

276
00:12:31,409 --> 00:12:41,088
clouds but could be used and deployed

277
00:12:34,009 --> 00:12:41,087
everywhere at the edge nvidia ejects

