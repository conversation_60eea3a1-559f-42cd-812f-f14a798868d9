# ``ArgumentParser/Argument``

## Topics

### Single Arguments

- ``init(help:completion:)-6pqzn``
- ``init(help:completion:)-4p94d``
- ``init(help:completion:transform:)-3fjtc``
- ``init(help:completion:transform:)-7yn32``
- ``init(wrappedValue:help:completion:)``
- ``init(wrappedValue:help:completion:transform:)``

### Array Arguments

- ``init(parsing:help:completion:)``
- ``init(parsing:help:completion:transform:)``
- ``init(wrappedValue:parsing:help:completion:)``
- ``init(wrappedValue:parsing:help:completion:transform:)``
- ``ArgumentArrayParsingStrategy``

### Infrequently Used APIs

- ``wrappedValue``
