# ``ArgumentParser/Flag``

## Topics

### Boolean Flags

- ``init(wrappedValue:name:help:)``

### Boolean Flags with Inversions

- ``init(wrappedValue:name:inversion:exclusivity:help:)``
- ``init(name:inversion:exclusivity:help:)-12okg``
- ``init(name:inversion:exclusivity:help:)-1h8f7``
- ``FlagInversion``

### Counted Flags

- ``init(name:help:)``

### Custom Enumerable Flags

- ``init(help:)``
- ``init(exclusivity:help:)-38n7u``
- ``init(exclusivity:help:)-5fggj``
- ``init(wrappedValue:exclusivity:help:)``
- ``init(wrappedValue:help:)``

### Infrequently Used APIs

- ``wrappedValue``

### Supporting Types

- ``FlagExclusivity``
