{"object": {"artifacts": [], "dependencies": [{"basedOn": null, "packageRef": {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser.git", "name": "swift-argument-parser"}, "state": {"checkoutState": {"revision": "c8ed701b513cf5177118a175d85fbbbcd707ab41", "version": "1.3.0"}, "name": "sourceControlCheckout"}, "subpath": "swift-argument-parser"}, {"basedOn": null, "packageRef": {"identity": "swift-transformers", "kind": "remoteSourceControl", "location": "https://github.com/huggingface/swift-transformers.git", "name": "swift-transformers"}, "state": {"checkoutState": {"revision": "fc6543263e4caed9bf6107466d625cfae9357f08", "version": "0.1.8"}, "name": "sourceControlCheckout"}, "subpath": "swift-transformers"}], "prebuilts": []}, "version": 7}