/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift'
'/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift'
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift
/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift
