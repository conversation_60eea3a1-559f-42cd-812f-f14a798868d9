{"builtTestProducts": [{"binaryPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.xctest/Contents/MacOS/whisperkitPackageTests", "packagePath": "/Users/<USER>/Desktop/english_learn/whisperkit", "productName": "whisperkitPackageTests"}], "copyCommands": {"/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/gpt2_tokenizer_config.json": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/FallbackConfigs/gpt2_tokenizer_config.json"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/gpt2_tokenizer_config.json"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/t5_tokenizer_config.json": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/FallbackConfigs/t5_tokenizer_config.json"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/t5_tokenizer_config.json"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/8_Channel_ID.m4a": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/8_Channel_ID.m4a"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/8_Channel_ID.m4a"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v02.json": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/config-v02.json"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v02.json"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v03.json": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/config-v03.json"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v03.json"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/es_test_clip.wav": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/es_test_clip.wav"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/es_test_clip.wav"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ja_test_clip.wav": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/ja_test_clip.wav"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ja_test_clip.wav"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk.wav": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/jfk.wav"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk.wav"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk_441khz.m4a": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/jfk_441khz.m4a"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk_441khz.m4a"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ted_60.m4a": {"inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/ted_60.m4a"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ted_60.m4a"}]}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/whisperkit-coreml": {"inputs": [{"kind": "directory", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Models/whisperkit-coreml"}], "outputs": [{"kind": "directory", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/whisperkit-coreml"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": ["GenerateManual"], "pluginDescriptions": [{"moduleC99Name": "GenerateManual", "moduleName": "GenerateManual", "package": "swift-argument-parser", "productNames": ["GenerateManual"], "sources": {"relativePaths": ["GenerateManualPlugin.swift", "GenerateManualPluginError.swift", "PackagePlugin+Helpers.swift"], "root": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Plugins/GenerateManual"}, "toolsVersion": {"_version": "5.8.0"}}], "swiftCommands": {"C.ArgumentParser-arm64-apple-macosx15.0-debug-tool.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources"}], "isLibrary": true, "moduleName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentParser-Swift.h", "-color-diagnostics", "-swift-version", "5", "-enable-experimental-feature", "StrictConcurrency", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build", "wholeModuleOptimization": false}, "C.ArgumentParser-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources"}], "isLibrary": true, "moduleName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentParser-Swift.h", "-color-diagnostics", "-swift-version", "5", "-enable-experimental-feature", "StrictConcurrency", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build", "wholeModuleOptimization": false}, "C.ArgumentParserToolInfo-arm64-apple-macosx15.0-debug-tool.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources"}], "isLibrary": true, "moduleName": "ArgumentParserToolInfo", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ToolInfo.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ArgumentParserToolInfo-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ToolInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build", "wholeModuleOptimization": false}, "C.ArgumentParserToolInfo-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources"}], "isLibrary": true, "moduleName": "ArgumentParserToolInfo", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ArgumentParserToolInfo-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build", "wholeModuleOptimization": false}, "C.Generation-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/Generation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/GenerationConfig.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources"}], "isLibrary": true, "moduleName": "Generation", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/Generation.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/GenerationConfig.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build", "wholeModuleOptimization": false}, "C.Hub-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Downloader.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Hub.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/HubApi.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<Hub-arm64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources"}], "isLibrary": true, "moduleName": "<PERSON><PERSON>", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Downloader.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Hub.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/HubApi.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build", "wholeModuleOptimization": false}, "C.HubCLI-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/HubCLI/HubCLI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources"}], "isLibrary": false, "moduleName": "HubCLI", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/HubCLI.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/HubCLI.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "HubCLI_main", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/HubCLI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/HubCLI.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/HubCLI/HubCLI.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build", "wholeModuleOptimization": false}, "C.Models-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModel.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModelTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources"}], "isLibrary": true, "moduleName": "Models", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/Models-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModel.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModelTypes.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build", "wholeModuleOptimization": false}, "C.TensorUtils-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsProcessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/RepetitionPenaltyWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TemperatureLogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopKLogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopPLogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLMultiArray+Utils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLShapedArray+Utils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/Math.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources"}], "isLibrary": true, "moduleName": "TensorUtils", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TensorUtils-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsProcessor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/RepetitionPenaltyWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TemperatureLogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopKLogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopPLogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLMultiArray+Utils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLShapedArray+Utils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/Math.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build", "wholeModuleOptimization": false}, "C.Tokenizers-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BPETokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BertTokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/ByteEncoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Decoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Normalizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PostProcessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PreTokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/TokenLattice.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Tokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Trie.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/UnigramTokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Utils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources"}], "isLibrary": true, "moduleName": "Tokenizers", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizers-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BPETokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BertTokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/ByteEncoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Decoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Normalizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PostProcessor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PreTokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/TokenLattice.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Tokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Trie.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/UnigramTokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Utils.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build", "wholeModuleOptimization": false}, "C.TransformersCLI-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TransformersCLI/main.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources"}], "isLibrary": false, "moduleName": "TransformersCLI", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TransformersCLI.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/main.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "TransformersCLI_main", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/main.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TransformersCLI.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TransformersCLI/main.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build", "wholeModuleOptimization": false}, "C.WhisperKit-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioChunker.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/AudioEncoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Configurations.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/FeatureExtractor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Models.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/ResultWriter.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/LogitsFilter.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/TokenSampler.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TextDecoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TranscribeTask.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Concurrency.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Utils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/WhisperKit.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources"}], "isLibrary": true, "moduleName": "WhisperKit", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioChunker.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/AudioEncoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Configurations.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/FeatureExtractor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Models.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/ResultWriter.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/LogitsFilter.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/TokenSampler.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TextDecoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TranscribeTask.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Concurrency.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Utils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/WhisperKit.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build", "wholeModuleOptimization": false}, "C.WhisperKitCLI-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/TranscribeCLI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/WhisperKitCLI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources"}], "isLibrary": false, "moduleName": "WhisperKitCLI", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitCLI.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "WhisperKitCLI_main", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitCLI.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/TranscribeCLI.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/WhisperKitCLI.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build", "wholeModuleOptimization": false}, "C.WhisperKitTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/DistanceCalculation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/NormalizeEn.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/SpellingMapping.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/WERUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTestUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/TestUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/UnitTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<WhisperKitTests-arm64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources"}], "isLibrary": true, "moduleName": "WhisperKitTests", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/DistanceCalculation.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/NormalizeEn.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/SpellingMapping.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/WERUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTestUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTests.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/TestUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/UnitTests.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build", "wholeModuleOptimization": false}, "C.generate-manual-arm64-apple-macosx15.0-debug-tool.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources"}], "isLibrary": false, "moduleName": "generate_manual", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/generate_manual.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "generate_manual_main", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/generate_manual.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build", "wholeModuleOptimization": false}, "C.generate-manual-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources"}], "isLibrary": false, "moduleName": "generate_manual", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/generate_manual.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/AuthorArgument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentSynopsis.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Author.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Authors.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Container.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Empty.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ForEach.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNodeWrapper.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocBuilder.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocComponent.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Document.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/DocumentDate.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Exit.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/List.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MultiPageDescription.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Name.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Preamble.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Section.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SeeAlso.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SinglePageDescription.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Synopsis.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentParser+MDoc.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Date+ExpressibleByArgument.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Process+SimpleAPI.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/GenerateManual.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNode.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocMacro.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocSerializationContext.swift.o", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/String+Escaping.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "generate_manual_main", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/AuthorArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentSynopsis.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Author.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Authors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Container.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Empty.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ForEach.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNodeWrapper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocBuilder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocComponent.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Document.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/DocumentDate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Exit.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/List.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MultiPageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Preamble.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Section.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SeeAlso.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SinglePageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Synopsis.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentParser+MDoc.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Date+ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Process+SimpleAPI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/GenerateManual.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNode.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocMacro.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocSerializationContext.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/String+Escaping.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/generate_manual.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build", "wholeModuleOptimization": false}, "C.whisperkitPackageTests-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources", "importPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources"}], "isLibrary": true, "moduleName": "whisperkitPackageTests", "moduleOutputPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/whisperkitPackageTests.swiftmodule", "objects": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/whisperkitPackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit"], "outputFileMapPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/whisperkitPackageTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift"], "tempsPath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"ArgumentParser": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentParser-Swift.h", "-color-diagnostics", "-swift-version", "5", "-enable-experimental-feature", "StrictConcurrency", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "ArgumentParserToolInfo": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "ArgumentParserToolInfo", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ArgumentParserToolInfo-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "Generation": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "Generation", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/Generation.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/GenerationConfig.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "Hub": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "<PERSON><PERSON>", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Downloader.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Hub.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/HubApi.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "HubCLI": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "HubCLI", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/HubCLI/HubCLI.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "HubCLI_main", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "Models": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "Models", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModel.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModelTypes.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/Models-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "TensorUtils": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "TensorUtils", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsProcessor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/RepetitionPenaltyWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TemperatureLogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopKLogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopPLogitsWarper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLMultiArray+Utils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLShapedArray+Utils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/Math.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TensorUtils-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "Tokenizers": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "Tokenizers", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BPETokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BertTokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/ByteEncoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Decoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Normalizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PostProcessor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PreTokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/TokenLattice.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Tokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Trie.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/UnigramTokenizer.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Utils.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizers-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "TransformersCLI": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "TransformersCLI", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TransformersCLI/main.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "TransformersCLI_main", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "WhisperKit": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "WhisperKit", "-package-name", "whisperkit", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioChunker.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/AudioEncoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Configurations.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/FeatureExtractor.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Models.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/ResultWriter.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/LogitsFilter.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/TokenSampler.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TextDecoder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TranscribeTask.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Concurrency.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Utils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/WhisperKit.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "WhisperKitCLI": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "WhisperKitCLI", "-package-name", "whisperkit", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIArguments.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/TranscribeCLI.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/WhisperKitCLI.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "WhisperKitCLI_main", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "WhisperKitTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "WhisperKitTests", "-package-name", "whisperkit", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/DistanceCalculation.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/NormalizeEn.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/SpellingMapping.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/WERUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTestUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTests.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/TestUtils.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/UnitTests.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx14.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "generate_manual": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "generate_manual", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "generate_manual_main", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-suppress-warnings", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "whisperkitPackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "whisperkitPackageTests", "-package-name", "whisperkit", "-incremental", "-c", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift", "-I", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/whisperkitPackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "whisperkit", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"ArgumentParser": ["ArgumentParserToolInfo"], "ArgumentParserToolInfo": [], "Generation": ["TensorUtils", "Tokenizers", "<PERSON><PERSON>"], "Hub": [], "HubCLI": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ArgumentParserToolInfo", "<PERSON><PERSON>"], "Models": ["Generation", "TensorUtils", "Tokenizers", "<PERSON><PERSON>"], "TensorUtils": [], "Tokenizers": ["<PERSON><PERSON>"], "TransformersCLI": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ArgumentParserToolInfo", "Models", "Generation", "TensorUtils", "Tokenizers", "<PERSON><PERSON>"], "WhisperKit": ["Models", "Generation", "TensorUtils", "Tokenizers", "<PERSON><PERSON>"], "WhisperKitCLI": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ArgumentParserToolInfo", "WhisperKit", "Models", "Generation", "TensorUtils", "Tokenizers", "<PERSON><PERSON>"], "WhisperKitTests": ["WhisperKit", "Models", "Generation", "TensorUtils", "Tokenizers", "<PERSON><PERSON>"], "generate_manual": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ArgumentParserToolInfo"], "whisperkitPackageTests": ["WhisperKitTests", "WhisperKit", "Models", "Generation", "TensorUtils", "Tokenizers", "<PERSON><PERSON>"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {"/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift": {"inputs": [], "outputs": [{"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift"}]}}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/Generation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/GenerationConfig.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Downloader.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Hub.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/HubApi.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/HubCLI/HubCLI.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModel.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModelTypes.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsProcessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/RepetitionPenaltyWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TemperatureLogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopKLogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopPLogitsWarper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLMultiArray+Utils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLShapedArray+Utils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/Math.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BPETokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BertTokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/ByteEncoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Decoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Normalizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PostProcessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PreTokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/TokenLattice.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Tokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Trie.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/UnigramTokenizer.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Utils.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TransformersCLI/main.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioChunker.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/AudioEncoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Configurations.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/FeatureExtractor.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Models.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/ResultWriter.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/LogitsFilter.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/TokenSampler.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TextDecoder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TranscribeTask.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Concurrency.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Utils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/WhisperKit.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIArguments.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/TranscribeCLI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/WhisperKitCLI.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/DistanceCalculation.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/NormalizeEn.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/SpellingMapping.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/WERUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTestUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/TestUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/UnitTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-entitlement.plist"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool-entitlement.plist"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ToolInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool.product/Objects.LinkFileList"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentParser+MDoc.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentSynopsis.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Author.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/AuthorArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Authors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Container.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Date+ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Document.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/DocumentDate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Empty.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Exit.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ForEach.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/GenerateManual.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/List.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNode.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNodeWrapper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocBuilder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocComponent.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocMacro.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocSerializationContext.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MultiPageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Preamble.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Process+SimpleAPI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Section.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SeeAlso.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SinglePageDescription.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/String+Escaping.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Synopsis.swift.o"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual.product/Objects.LinkFileList"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli-entitlement.plist"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/HubCLI.swift.o"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli.product/Objects.LinkFileList"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers-entitlement.plist"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/main.swift.o"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers.product/Objects.LinkFileList"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli-entitlement.plist"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli.product/Objects.LinkFileList"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o"}], "outputFilePath": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.product/Objects.LinkFileList"}}}