{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/DistanceCalculation.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/NormalizeEn.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/SpellingMapping.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/WERUtils.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTestUtils.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTests.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/TestUtils.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/UnitTests.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swiftdeps"}}