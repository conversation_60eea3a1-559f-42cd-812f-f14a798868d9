{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsProcessor.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsWarper.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/RepetitionPenaltyWarper.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TemperatureLogitsWarper.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopKLogitsWarper.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopPLogitsWarper.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLMultiArray+Utils.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLShapedArray+Utils.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/Math.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swiftdeps"}}