{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swiftdeps"}}