{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swiftdeps"}}