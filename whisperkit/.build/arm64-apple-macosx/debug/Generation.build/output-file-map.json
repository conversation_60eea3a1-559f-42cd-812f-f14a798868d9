{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/Generation.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/GenerationConfig.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swiftdeps"}}