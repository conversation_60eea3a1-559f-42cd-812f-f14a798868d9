{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIArguments.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIUtils.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/TranscribeCLI.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/WhisperKitCLI.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swiftdeps"}}