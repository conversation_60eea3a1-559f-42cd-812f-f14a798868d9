{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioChunker.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/AudioEncoder.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Configurations.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/FeatureExtractor.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Models.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/ResultWriter.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/LogitsFilter.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/TokenSampler.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TextDecoder.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TranscribeTask.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Concurrency.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Utils.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/WhisperKit.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swiftdeps"}}