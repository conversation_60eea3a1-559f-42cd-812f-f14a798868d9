/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o
/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o
