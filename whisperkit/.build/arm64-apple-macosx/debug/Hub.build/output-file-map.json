{"": {"swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/master.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Downloader.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Hub.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/HubApi.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swiftdeps"}, "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/DerivedSources/resource_bundle_accessor.swift": {"dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.d", "object": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swiftdeps"}}