client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "ArgumentParser-arm64-apple-macosx15.0-debug-tool.module": ["<ArgumentParser-arm64-apple-macosx15.0-debug-tool.module>"]
  "ArgumentParser-arm64-apple-macosx15.0-debug.module": ["<ArgumentParser-arm64-apple-macosx15.0-debug.module>"]
  "ArgumentParserToolInfo-arm64-apple-macosx15.0-debug-tool.module": ["<ArgumentParserToolInfo-arm64-apple-macosx15.0-debug-tool.module>"]
  "ArgumentParserToolInfo-arm64-apple-macosx15.0-debug.module": ["<ArgumentParserToolInfo-arm64-apple-macosx15.0-debug.module>"]
  "Generation-arm64-apple-macosx15.0-debug.module": ["<Generation-arm64-apple-macosx15.0-debug.module>"]
  "Hub-arm64-apple-macosx15.0-debug.module": ["<Hub-arm64-apple-macosx15.0-debug.module>"]
  "HubCLI-arm64-apple-macosx15.0-debug.module": ["<HubCLI-arm64-apple-macosx15.0-debug.module>"]
  "Models-arm64-apple-macosx15.0-debug.module": ["<Models-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "TensorUtils-arm64-apple-macosx15.0-debug.module": ["<TensorUtils-arm64-apple-macosx15.0-debug.module>"]
  "Tokenizers-arm64-apple-macosx15.0-debug.module": ["<Tokenizers-arm64-apple-macosx15.0-debug.module>"]
  "TransformersCLI-arm64-apple-macosx15.0-debug.module": ["<TransformersCLI-arm64-apple-macosx15.0-debug.module>"]
  "WhisperKit-arm64-apple-macosx15.0-debug.module": ["<WhisperKit-arm64-apple-macosx15.0-debug.module>"]
  "WhisperKitCLI-arm64-apple-macosx15.0-debug.module": ["<WhisperKitCLI-arm64-apple-macosx15.0-debug.module>"]
  "WhisperKitTests-arm64-apple-macosx15.0-debug.module": ["<WhisperKitTests-arm64-apple-macosx15.0-debug.module>"]
  "generate-manual-arm64-apple-macosx15.0-debug-tool.exe": ["<generate-manual-arm64-apple-macosx15.0-debug-tool.exe>"]
  "generate-manual-arm64-apple-macosx15.0-debug-tool.module": ["<generate-manual-arm64-apple-macosx15.0-debug-tool.module>"]
  "generate-manual-arm64-apple-macosx15.0-debug.exe": ["<generate-manual-arm64-apple-macosx15.0-debug.exe>"]
  "generate-manual-arm64-apple-macosx15.0-debug.module": ["<generate-manual-arm64-apple-macosx15.0-debug.module>"]
  "hub-cli-arm64-apple-macosx15.0-debug.exe": ["<hub-cli-arm64-apple-macosx15.0-debug.exe>"]
  "main": ["<WhisperKit-arm64-apple-macosx15.0-debug.module>","<WhisperKitCLI-arm64-apple-macosx15.0-debug.module>","<generate-manual-arm64-apple-macosx15.0-debug-tool.exe>","<generate-manual-arm64-apple-macosx15.0-debug.exe>","<hub-cli-arm64-apple-macosx15.0-debug.exe>","<transformers-arm64-apple-macosx15.0-debug.exe>","<whisperkit-cli-arm64-apple-macosx15.0-debug.exe>"]
  "test": ["<WhisperKit-arm64-apple-macosx15.0-debug.module>","<WhisperKitCLI-arm64-apple-macosx15.0-debug.module>","<WhisperKitTests-arm64-apple-macosx15.0-debug.module>","<generate-manual-arm64-apple-macosx15.0-debug-tool.exe>","<generate-manual-arm64-apple-macosx15.0-debug.exe>","<hub-cli-arm64-apple-macosx15.0-debug.exe>","<transformers-arm64-apple-macosx15.0-debug.exe>","<whisperkit-cli-arm64-apple-macosx15.0-debug.exe>","<whisperkitPackageTests-arm64-apple-macosx15.0-debug.test>"]
  "transformers-arm64-apple-macosx15.0-debug.exe": ["<transformers-arm64-apple-macosx15.0-debug.exe>"]
  "whisperkit-cli-arm64-apple-macosx15.0-debug.exe": ["<whisperkit-cli-arm64-apple-macosx15.0-debug.exe>"]
  "whisperkitPackageTests-arm64-apple-macosx15.0-debug.module": ["<whisperkitPackageTests-arm64-apple-macosx15.0-debug.module>"]
  "whisperkitPackageTests-arm64-apple-macosx15.0-debug.test": ["<whisperkitPackageTests-arm64-apple-macosx15.0-debug.test>"]
default: "main"
nodes:
  "/Users/<USER>/Desktop/english_learn/whisperkit/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual":
    is-mutated: true
  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool":
    is-mutated: true
  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli":
    is-mutated: true
  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers":
    is-mutated: true
  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli":
    is-mutated: true
commands:
  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/Generation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/GenerationConfig.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Downloader.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Hub.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/HubApi.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/HubCLI/HubCLI.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModel.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModelTypes.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsProcessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/RepetitionPenaltyWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TemperatureLogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopKLogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopPLogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLMultiArray+Utils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLShapedArray+Utils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/Math.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BPETokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BertTokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/ByteEncoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Decoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Normalizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PostProcessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PreTokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/TokenLattice.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Tokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Trie.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/UnigramTokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Utils.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TransformersCLI/main.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioChunker.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/AudioEncoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Configurations.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/FeatureExtractor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Models.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/ResultWriter.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/LogitsFilter.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/TokenSampler.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TextDecoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TranscribeTask.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Concurrency.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Utils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/WhisperKit.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/TranscribeCLI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/WhisperKitCLI.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/DistanceCalculation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/NormalizeEn.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/SpellingMapping.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/WERUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTestUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTests.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/TestUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/UnitTests.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-entitlement.plist"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool-entitlement.plist"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool.product/Objects.LinkFileList"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Synopsis.swift.o"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual.product/Objects.LinkFileList"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli-entitlement.plist"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/HubCLI.swift.o"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli.product/Objects.LinkFileList"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/gpt2_tokenizer_config.json":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/FallbackConfigs/gpt2_tokenizer_config.json"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/gpt2_tokenizer_config.json"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/FallbackConfigs/gpt2_tokenizer_config.json"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/t5_tokenizer_config.json":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/FallbackConfigs/t5_tokenizer_config.json"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/t5_tokenizer_config.json"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/FallbackConfigs/t5_tokenizer_config.json"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers-entitlement.plist"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/main.swift.o"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers.product/Objects.LinkFileList"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli-entitlement.plist"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli.product/Objects.LinkFileList"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift":
    tool: test-entry-point-tool
    inputs: []
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift"]

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.product/Objects.LinkFileList"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/8_Channel_ID.m4a":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/8_Channel_ID.m4a"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/8_Channel_ID.m4a"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/8_Channel_ID.m4a"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v02.json":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/config-v02.json"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v02.json"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/config-v02.json"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v03.json":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/config-v03.json"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v03.json"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/config-v03.json"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/es_test_clip.wav":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/es_test_clip.wav"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/es_test_clip.wav"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/es_test_clip.wav"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ja_test_clip.wav":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/ja_test_clip.wav"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ja_test_clip.wav"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/ja_test_clip.wav"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk.wav":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/jfk.wav"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk.wav"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/jfk.wav"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk_441khz.m4a":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/jfk_441khz.m4a"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk_441khz.m4a"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/jfk_441khz.m4a"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ted_60.m4a":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/ted_60.m4a"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ted_60.m4a"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Resources/ted_60.m4a"

  "/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/whisperkit-coreml":
    tool: copy-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Models/whisperkit-coreml/"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/whisperkit-coreml/"]
    description: "Copying /Users/<USER>/Desktop/english_learn/whisperkit/Models/whisperkit-coreml"

  "<ArgumentParser-arm64-apple-macosx15.0-debug-tool.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule"]
    outputs: ["<ArgumentParser-arm64-apple-macosx15.0-debug-tool.module>"]

  "<ArgumentParser-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule"]
    outputs: ["<ArgumentParser-arm64-apple-macosx15.0-debug.module>"]

  "<ArgumentParserToolInfo-arm64-apple-macosx15.0-debug-tool.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule"]
    outputs: ["<ArgumentParserToolInfo-arm64-apple-macosx15.0-debug-tool.module>"]

  "<ArgumentParserToolInfo-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule"]
    outputs: ["<ArgumentParserToolInfo-arm64-apple-macosx15.0-debug.module>"]

  "<Generation-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule"]
    outputs: ["<Generation-arm64-apple-macosx15.0-debug.module>"]

  "<Hub-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule"]
    outputs: ["<Hub-arm64-apple-macosx15.0-debug.module>"]

  "<HubCLI-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/HubCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/HubCLI.swiftmodule"]
    outputs: ["<HubCLI-arm64-apple-macosx15.0-debug.module>"]

  "<Models-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule"]
    outputs: ["<Models-arm64-apple-macosx15.0-debug.module>"]

  "<TensorUtils-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule"]
    outputs: ["<TensorUtils-arm64-apple-macosx15.0-debug.module>"]

  "<Tokenizers-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"]
    outputs: ["<Tokenizers-arm64-apple-macosx15.0-debug.module>"]

  "<TransformersCLI-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/main.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TransformersCLI.swiftmodule"]
    outputs: ["<TransformersCLI-arm64-apple-macosx15.0-debug.module>"]

  "<WhisperKit-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule"]
    outputs: ["<WhisperKit-arm64-apple-macosx15.0-debug.module>"]

  "<WhisperKitCLI-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitCLI.swiftmodule"]
    outputs: ["<WhisperKitCLI-arm64-apple-macosx15.0-debug.module>"]

  "<WhisperKitTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule"]
    outputs: ["<WhisperKitTests-arm64-apple-macosx15.0-debug.module>"]

  "<generate-manual-arm64-apple-macosx15.0-debug-tool.exe>":
    tool: phony
    inputs: ["<generate-manual-arm64-apple-macosx15.0-debug-tool.exe-CodeSigning>"]
    outputs: ["<generate-manual-arm64-apple-macosx15.0-debug-tool.exe>"]

  "<generate-manual-arm64-apple-macosx15.0-debug-tool.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/generate_manual.swiftmodule"]
    outputs: ["<generate-manual-arm64-apple-macosx15.0-debug-tool.module>"]

  "<generate-manual-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<generate-manual-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<generate-manual-arm64-apple-macosx15.0-debug.exe>"]

  "<generate-manual-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Synopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/generate_manual.swiftmodule"]
    outputs: ["<generate-manual-arm64-apple-macosx15.0-debug.module>"]

  "<hub-cli-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<hub-cli-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<hub-cli-arm64-apple-macosx15.0-debug.exe>"]

  "<transformers-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<transformers-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<transformers-arm64-apple-macosx15.0-debug.exe>"]

  "<whisperkit-cli-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<whisperkit-cli-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<whisperkit-cli-arm64-apple-macosx15.0-debug.exe>"]

  "<whisperkitPackageTests-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/whisperkitPackageTests.swiftmodule"]
    outputs: ["<whisperkitPackageTests-arm64-apple-macosx15.0-debug.module>"]

  "<whisperkitPackageTests-arm64-apple-macosx15.0-debug.test>":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.xctest/Contents/MacOS/whisperkitPackageTests"]
    outputs: ["<whisperkitPackageTests-arm64-apple-macosx15.0-debug.test>"]

  "C.ArgumentParser-arm64-apple-macosx15.0-debug-tool.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule"]
    description: "Compiling Swift Module 'ArgumentParser' (41 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ArgumentParser","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentParser-Swift.h","-color-diagnostics","-swift-version","5","-enable-experimental-feature","StrictConcurrency","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.ArgumentParser-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule"]
    description: "Compiling Swift Module 'ArgumentParser' (41 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ArgumentParser","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentParser-Swift.h","-color-diagnostics","-swift-version","5","-enable-experimental-feature","StrictConcurrency","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.ArgumentParserToolInfo-arm64-apple-macosx15.0-debug-tool.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule"]
    description: "Compiling Swift Module 'ArgumentParserToolInfo' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ArgumentParserToolInfo","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ArgumentParserToolInfo-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.ArgumentParserToolInfo-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule"]
    description: "Compiling Swift Module 'ArgumentParserToolInfo' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ArgumentParserToolInfo","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ArgumentParserToolInfo-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.Generation-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/Generation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Generation/GenerationConfig.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule"]
    description: "Compiling Swift Module 'Generation' (2 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","Generation","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.Hub-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Downloader.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/Hub.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Hub/HubApi.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<Hub-arm64-apple-macosx15.0-debug.module-resources>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule"]
    description: "Compiling Swift Module 'Hub' (4 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","Hub","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.HubCLI-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/HubCLI/HubCLI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/HubCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/HubCLI.swiftmodule"]
    description: "Compiling Swift Module 'HubCLI' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","HubCLI","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/HubCLI.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/output-file-map.json","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","HubCLI_main","-parse-as-library","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.Models-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModel.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Models/LanguageModelTypes.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule"]
    description: "Compiling Swift Module 'Models' (2 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","Models","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/Models-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.TensorUtils-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsProcessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/LogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/RepetitionPenaltyWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TemperatureLogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopKLogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/LogitsWarper/TopPLogitsWarper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLMultiArray+Utils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/MLShapedArray+Utils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TensorUtils/Math.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule"]
    description: "Compiling Swift Module 'TensorUtils' (9 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","TensorUtils","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TensorUtils-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.Tokenizers-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BPETokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/BertTokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/ByteEncoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Decoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Normalizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PostProcessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/PreTokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/TokenLattice.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Tokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Trie.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/UnigramTokenizer.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/Tokenizers/Utils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule"]
    description: "Compiling Swift Module 'Tokenizers' (12 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","Tokenizers","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizers-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.TransformersCLI-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-transformers/Sources/TransformersCLI/main.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/main.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TransformersCLI.swiftmodule"]
    description: "Compiling Swift Module 'TransformersCLI' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","TransformersCLI","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TransformersCLI.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/output-file-map.json","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","TransformersCLI_main","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.WhisperKit-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioChunker.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/AudioEncoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Configurations.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/FeatureExtractor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Models.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/ResultWriter.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/LogitsFilter.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Text/TokenSampler.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TextDecoder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/TranscribeTask.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Concurrency.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/Utils/Utils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/Core/WhisperKit.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule"]
    description: "Compiling Swift Module 'WhisperKit' (18 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","WhisperKit","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","whisperkit"]

  "C.WhisperKitCLI-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIArguments.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/CLIUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/TranscribeCLI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/WhisperKitCLI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitCLI.swiftmodule"]
    description: "Compiling Swift Module 'WhisperKitCLI' (4 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","WhisperKitCLI","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitCLI.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/output-file-map.json","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","WhisperKitCLI_main","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","whisperkit"]

  "C.WhisperKitTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/DistanceCalculation.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/NormalizeEn.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/SpellingMapping.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/Evaluate/WERUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/FunctionalTests.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTestUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/RegressionTests.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/TestUtils.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Tests/WhisperKitTests/UnitTests.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<WhisperKitTests-arm64-apple-macosx15.0-debug.module-resources>","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule"]
    description: "Compiling Swift Module 'WhisperKitTests' (10 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","WhisperKitTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx14.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","whisperkit"]

  "C.generate-manual-arm64-apple-macosx15.0-debug-tool.exe-tool":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo-tool.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool"]
    description: "Linking ./.build/arm64-apple-macosx/debug/generate-manual-tool"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool","-module-name","generate_manual","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_generate_manual_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool.product/Objects.LinkFileList","-Xlinker","-rpath","-Xlinker","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.5/macosx","-target","arm64-apple-macosx10.13","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/generate_manual.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.generate-manual-arm64-apple-macosx15.0-debug-tool.exe-tool-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool-entitlement.plist"]
    outputs: ["<generate-manual-arm64-apple-macosx15.0-debug-tool.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/generate-manual-tool"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool-entitlement.plist","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-tool"]

  "C.generate-manual-arm64-apple-macosx15.0-debug-tool.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParser.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/ArgumentParserToolInfo.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Synopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/generate_manual.swiftmodule"]
    description: "Compiling Swift Module 'generate_manual' (29 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","generate_manual","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool/generate_manual.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/output-file-map.json","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual-tool.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules-tool","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","generate_manual_main","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.generate-manual-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Synopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual"]
    description: "Linking ./.build/arm64-apple-macosx/debug/generate-manual"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual","-module-name","generate_manual","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_generate_manual_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual.product/Objects.LinkFileList","-Xlinker","-rpath","-Xlinker","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.5/macosx","-target","arm64-apple-macosx10.13","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/generate_manual.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.generate-manual-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-entitlement.plist"]
    outputs: ["<generate-manual-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/generate-manual"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual-entitlement.plist","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate-manual"]

  "C.generate-manual-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/AuthorArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/ArgumentSynopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Author.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Authors.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Container.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/Empty.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/ForEach.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocASTNodeWrapper.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocBuilder.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Core/MDocComponent.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Document.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/DocumentDate.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Exit.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/List.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/MultiPageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Name.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Preamble.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Section.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SeeAlso.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/SinglePageDescription.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/DSL/Synopsis.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/ArgumentParser+MDoc.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Date+ExpressibleByArgument.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/Extensions/Process+SimpleAPI.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/GenerateManual.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocASTNode.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocMacro.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/MDocSerializationContext.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/checkouts/swift-argument-parser/Tools/generate-manual/MDoc/String+Escaping.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/AuthorArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentSynopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Author.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Authors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Container.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Empty.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ForEach.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNodeWrapper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocBuilder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocComponent.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Document.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/DocumentDate.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Exit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/List.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MultiPageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Preamble.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Section.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SeeAlso.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/SinglePageDescription.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Synopsis.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/ArgumentParser+MDoc.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Date+ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/Process+SimpleAPI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/GenerateManual.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocASTNode.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocMacro.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/MDocSerializationContext.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/String+Escaping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/generate_manual.swiftmodule"]
    description: "Compiling Swift Module 'generate_manual' (29 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","generate_manual","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/generate_manual.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/output-file-map.json","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/generate_manual.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","generate_manual_main","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.hub-cli-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/HubCLI.build/HubCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli"]
    description: "Linking ./.build/arm64-apple-macosx/debug/hub-cli"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli","-module-name","hub_cli","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_HubCLI_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli.product/Objects.LinkFileList","-target","arm64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/HubCLI.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.hub-cli-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli-entitlement.plist"]
    outputs: ["<hub-cli-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/hub-cli"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli-entitlement.plist","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/hub-cli"]

  "C.transformers-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TransformersCLI.build/main.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers"]
    description: "Linking ./.build/arm64-apple-macosx/debug/transformers"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers","-module-name","transformers","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_TransformersCLI_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers.product/Objects.LinkFileList","-target","arm64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TransformersCLI.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.transformers-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers-entitlement.plist"]
    outputs: ["<transformers-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/transformers"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers-entitlement.plist","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/transformers"]

  "C.whisperkit-cli-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIArguments.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/CLIUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/TranscribeCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitCLI.build/WhisperKitCLI.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli"]
    description: "Linking ./.build/arm64-apple-macosx/debug/whisperkit-cli"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli","-module-name","whisperkit_cli","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_WhisperKitCLI_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli.product/Objects.LinkFileList","-target","arm64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParser.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/ArgumentParserToolInfo.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitCLI.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.whisperkit-cli-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli-entitlement.plist"]
    outputs: ["<whisperkit-cli-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/whisperkit-cli"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli-entitlement.plist","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit-cli"]

  "C.whisperkitPackageTests-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.derived/runner.swift","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/whisperkitPackageTests.swiftmodule"]
    description: "Compiling Swift Module 'whisperkitPackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","whisperkitPackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/whisperkitPackageTests.swiftmodule","-output-file-map","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/sources","-I","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/whisperkitPackageTests-Swift.h","-color-diagnostics","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","whisperkit"]

  "C.whisperkitPackageTests-arm64-apple-macosx15.0-debug.test":
    tool: shell
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/Generation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Generation.build/GenerationConfig.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Downloader.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/Hub.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/HubApi.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Hub.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModel.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Models.build/LanguageModelTypes.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/LogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLMultiArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/MLShapedArray+Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/Math.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/RepetitionPenaltyWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TemperatureLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopKLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/TensorUtils.build/TopPLogitsWarper.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BPETokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/BertTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/ByteEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Decoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Normalizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PostProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/PreTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/TokenLattice.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Tokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Trie.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/UnigramTokenizer.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Tokenizers.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioChunker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioEncoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioProcessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/AudioStreamTranscriber.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Concurrency.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Configurations.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/EnergyVAD.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/FeatureExtractor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/LogitsFilter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Models.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/ResultWriter.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/SegmentSeeker.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TextDecoder.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TokenSampler.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/TranscribeTask.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/Utils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/VoiceActivityDetector.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKit.build/WhisperKit.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/DistanceCalculation.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/FunctionalTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/NormalizeEn.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/RegressionTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/SpellingMapping.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/TestUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/UnitTests.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/WERUtils.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/WhisperKitTests.build/resource_bundle_accessor.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.build/runner.swift.o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.xctest/Contents/MacOS/whisperkitPackageTests"]
    description: "Linking ./.build/arm64-apple-macosx/debug/whisperkitPackageTests.xctest/Contents/MacOS/whisperkitPackageTests"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.xctest/Contents/MacOS/whisperkitPackageTests","-module-name","whisperkitPackageTests","-Xlinker","-no_warn_duplicate_libraries","-Xlinker","-bundle","-Xlinker","-rpath","-Xlinker","@loader_path/../../../","@/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkitPackageTests.product/Objects.LinkFileList","-target","arm64-apple-macosx14.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Generation.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Hub.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Models.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/TensorUtils.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/Tokenizers.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKit.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/WhisperKitTests.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/Modules/whisperkitPackageTests.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "Hub-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/gpt2_tokenizer_config.json","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/swift-transformers_Hub.bundle/t5_tokenizer_config.json"]
    outputs: ["<Hub-arm64-apple-macosx15.0-debug.module-resources>"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKit/","/Users/<USER>/Desktop/english_learn/whisperkit/Sources/WhisperKitCLI/","/Users/<USER>/Desktop/english_learn/whisperkit/Package.swift","/Users/<USER>/Desktop/english_learn/whisperkit/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

  "WhisperKitTests-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/whisperkit-coreml/","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/8_Channel_ID.m4a","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v02.json","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/config-v03.json","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/es_test_clip.wav","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ja_test_clip.wav","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk.wav","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/jfk_441khz.m4a","/Users/<USER>/Desktop/english_learn/whisperkit/.build/arm64-apple-macosx/debug/whisperkit_WhisperKitTests.bundle/ted_60.m4a"]
    outputs: ["<WhisperKitTests-arm64-apple-macosx15.0-debug.module-resources>"]

